using KiloAccounting.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace KiloAccounting.Services
{
    public interface ICompanyService
    {
        Task<Company> GetCompanyAsync();
        Task<Company> UpdateCompanyAsync(Company company);
        Task<bool> UpdateLogoAsync(byte[] logoData);
    }
}

namespace KiloAccounting.Services
{
    public interface IAccountService
    {
        Task<IEnumerable<Account>> GetAllAccountsAsync();
        Task<Account> GetAccountByIdAsync(int accountId);
        Task<Account> GetAccountByCodeAsync(string accountCode);
        Task<IEnumerable<Account>> GetAccountsByTypeAsync(AccountType accountType);
        Task<IEnumerable<Account>> GetAccountsByCategoryAsync(AccountCategory category);
        Task<IEnumerable<Account>> GetParentAccountsAsync();
        Task<IEnumerable<Account>> GetSubAccountsAsync(int parentAccountId);
        Task<Account> CreateAccountAsync(Account account);
        Task<Account> UpdateAccountAsync(Account account);
        Task<bool> DeleteAccountAsync(int accountId);
        Task<bool> IsAccountCodeAvailableAsync(string accountCode);
        Task<decimal> GetAccountBalanceAsync(int accountId);
        Task UpdateAccountBalanceAsync(int accountId, decimal amount, bool isDebit);
    }
}

namespace KiloAccounting.Services
{
    public interface ICustomerService
    {
        Task<IEnumerable<Customer>> GetAllCustomersAsync();
        Task<Customer> GetCustomerByIdAsync(int customerId);
        Task<Customer> GetCustomerByCodeAsync(string customerCode);
        Task<Customer> CreateCustomerAsync(Customer customer);
        Task<Customer> UpdateCustomerAsync(Customer customer);
        Task<bool> DeleteCustomerAsync(int customerId);
        Task<bool> IsCustomerCodeAvailableAsync(string customerCode);
        Task<decimal> GetCustomerBalanceAsync(int customerId);
        Task<IEnumerable<CustomerTransaction>> GetCustomerTransactionsAsync(int customerId);
        Task AddCustomerTransactionAsync(CustomerTransaction transaction);
    }
}

namespace KiloAccounting.Services
{
    public interface ISupplierService
    {
        Task<IEnumerable<Supplier>> GetAllSuppliersAsync();
        Task<Supplier> GetSupplierByIdAsync(int supplierId);
        Task<Supplier> GetSupplierByCodeAsync(string supplierCode);
        Task<Supplier> CreateSupplierAsync(Supplier supplier);
        Task<Supplier> UpdateSupplierAsync(Supplier supplier);
        Task<bool> DeleteSupplierAsync(int supplierId);
        Task<bool> IsSupplierCodeAvailableAsync(string supplierCode);
        Task<decimal> GetSupplierBalanceAsync(int supplierId);
        Task<IEnumerable<SupplierTransaction>> GetSupplierTransactionsAsync(int supplierId);
        Task AddSupplierTransactionAsync(SupplierTransaction transaction);
    }
}

namespace KiloAccounting.Services
{
    public interface IInventoryService
    {
        Task<IEnumerable<Product>> GetAllProductsAsync();
        Task<Product> GetProductByIdAsync(int productId);
        Task<Product> GetProductByCodeAsync(string productCode);
        Task<Product> GetProductByBarcodeAsync(string barcode);
        Task<Product> CreateProductAsync(Product product);
        Task<Product> UpdateProductAsync(Product product);
        Task<bool> DeleteProductAsync(int productId);
        Task<bool> IsProductCodeAvailableAsync(string productCode);
        Task<bool> IsBarcodeAvailableAsync(string barcode);
        Task<IEnumerable<ProductCategory>> GetAllCategoriesAsync();
        Task<IEnumerable<Unit>> GetAllUnitsAsync();
        Task UpdateStockAsync(int productId, decimal quantity, InventoryTransactionType transactionType, string referenceNumber = null);
        Task<IEnumerable<Product>> GetLowStockProductsAsync();
        Task<IEnumerable<InventoryTransaction>> GetInventoryTransactionsAsync(int? productId = null);
    }
}

namespace KiloAccounting.Services
{
    public interface IInvoiceService
    {
        Task<IEnumerable<Invoice>> GetAllInvoicesAsync();
        Task<Invoice> GetInvoiceByIdAsync(int invoiceId);
        Task<Invoice> GetInvoiceByNumberAsync(string invoiceNumber);
        Task<Invoice> CreateInvoiceAsync(Invoice invoice);
        Task<Invoice> UpdateInvoiceAsync(Invoice invoice);
        Task<bool> DeleteInvoiceAsync(int invoiceId);
        Task<bool> PostInvoiceAsync(int invoiceId, int userId);
        Task<string> GenerateInvoiceNumberAsync(InvoiceType invoiceType);
        Task<IEnumerable<PurchaseInvoice>> GetAllPurchaseInvoicesAsync();
        Task<PurchaseInvoice> GetPurchaseInvoiceByIdAsync(int purchaseInvoiceId);
        Task<PurchaseInvoice> CreatePurchaseInvoiceAsync(PurchaseInvoice purchaseInvoice);
        Task<PurchaseInvoice> UpdatePurchaseInvoiceAsync(PurchaseInvoice purchaseInvoice);
        Task<bool> DeletePurchaseInvoiceAsync(int purchaseInvoiceId);
        Task<bool> PostPurchaseInvoiceAsync(int purchaseInvoiceId, int userId);
    }
}

namespace KiloAccounting.Services
{
    public interface IReportService
    {
        Task<byte[]> GenerateIncomeStatementAsync(DateTime fromDate, DateTime toDate);
        Task<byte[]> GenerateBalanceSheetAsync(DateTime asOfDate);
        Task<byte[]> GenerateTrialBalanceAsync(DateTime asOfDate);
        Task<byte[]> GenerateCustomerStatementAsync(int customerId, DateTime fromDate, DateTime toDate);
        Task<byte[]> GenerateSupplierStatementAsync(int supplierId, DateTime fromDate, DateTime toDate);
        Task<byte[]> GenerateInventoryReportAsync();
        Task<byte[]> GenerateSalesReportAsync(DateTime fromDate, DateTime toDate);
        Task<byte[]> GeneratePurchaseReportAsync(DateTime fromDate, DateTime toDate);
    }
}

namespace KiloAccounting.Services
{
    public interface IBackupService
    {
        Task<bool> CreateBackupAsync(string backupPath);
        Task<bool> RestoreBackupAsync(string backupPath);
        Task<IEnumerable<string>> GetAvailableBackupsAsync();
        Task<bool> ScheduleAutoBackupAsync();
    }
}

namespace KiloAccounting.Services
{
    public interface ILocalizationService
    {
        string GetString(string key);
        void SetCulture(string culture);
        string CurrentCulture { get; }
        IEnumerable<string> AvailableCultures { get; }
    }
}

namespace KiloAccounting.Services
{
    public interface IExportService
    {
        Task<byte[]> ExportToExcelAsync<T>(IEnumerable<T> data, string sheetName);
        Task<byte[]> ExportToPdfAsync(string html);
        Task<bool> ExportInvoiceToPdfAsync(int invoiceId, string filePath);
        Task<bool> ExportReportToPdfAsync(string reportHtml, string filePath);
    }
}