# نظام كيلو للمحاسبة المالية
## Kilo Financial Accounting System

نظام محاسبة مالية احترافي مخصص للشركات باستخدام C# و WPF مع تصميم عصري وواجهة مستخدم متقدمة.

A professional financial accounting system designed for companies using C# and WPF with modern design and advanced user interface.

## المميزات الرئيسية / Key Features

### 🔐 نظام المستخدمين / User Management
- تسجيل دخول آمن مع صلاحيات متعددة
- أدوار المستخدمين: مدير، محاسب، موظف مبيعات، موظف مشتريات، موظف مخزون، مستعلم
- إدارة كلمات المرور وتشفيرها

### 💰 إدارة الفواتير / Invoice Management
- فواتير المبيعات ومردودات المبيعات
- فواتير المشتريات ومردودات المشتريات
- ترقيم تلقائي للفواتير
- حالات متعددة للفواتير (مسودة، معتمدة، مدفوعة، إلخ)

### 📦 إدارة المخزون / Inventory Management
- إضافة وتعديل وحذف الأصناف
- تصنيف الأصناف حسب الفئات
- إدارة الوحدات والمقاييس
- تتبع حركات المخزون
- تنبيهات للأصناف منخفضة المخزون

### 👥 إدارة العملاء والموردين / Customer & Supplier Management
- بيانات تفصيلية للعملاء والموردين
- كشوف حساب شاملة
- إدارة أرصدة أول المدة
- تتبع المعاملات المالية

### 📊 النظام المحاسبي / Accounting System
- شجرة حسابات مرنة قابلة للتخصيص
- إدخالات يومية (قيود محاسبية)
- ميزان مراجعة
- دفتر الأستاذ العام

### 📈 التقارير المالية / Financial Reports
- قائمة الدخل
- الميزانية العمومية
- الحسابات الختامية
- تقارير الأرباح والخسائر
- تقارير المخزون والمبيعات

### 🤝 حسابات الشركاء / Partnership Accounting
- إدارة نسب الشراكة
- توزيع الأرباح
- تتبع رأس المال والتغيرات

### ⚙️ الإعدادات العامة / General Settings
- إعدادات الشركة والشعار
- إعداد السنة المالية
- إعدادات الضرائب (ضريبة القيمة المضافة)
- دعم تعدد اللغات (العربية والإنجليزية)

### 🔧 ميزات إضافية / Additional Features
- نظام نسخ احتياطي واستعادة البيانات
- تصدير التقارير بصيغ Excel و PDF
- واجهة مستخدم عصرية باستخدام Material Design
- دعم RTL للغة العربية

## المتطلبات التقنية / Technical Requirements

### البيئة التطويرية / Development Environment
- .NET 6.0 أو أحدث
- Visual Studio 2022 أو أحدث
- Windows 7/10/11 أو أحدث

### المكتبات المستخدمة / Dependencies
- **MaterialDesignThemes** - للتصميم العصري
- **Entity Framework Core** - لإدارة قاعدة البيانات
- **SQLite** - قاعدة البيانات المحلية
- **BCrypt.Net** - لتشفير كلمات المرور
- **EPPlus** - لتصدير Excel
- **iTextSharp** - لتصدير PDF

## التثبيت والتشغيل / Installation & Setup

### 1. استنساخ المشروع / Clone the Project
```bash
git clone https://github.com/your-repo/kilo-accounting.git
cd kilo-accounting
```

### 2. استعادة الحزم / Restore Packages
```bash
dotnet restore
```

### 3. بناء المشروع / Build the Project
```bash
dotnet build
```

### 4. تشغيل التطبيق / Run the Application
```bash
dotnet run
```

## بيانات تسجيل الدخول الافتراضية / Default Login Credentials

### المدير / Administrator
- **اسم المستخدم / Username:** admin
- **كلمة المرور / Password:** admin123

### المحاسب / Accountant
- **اسم المستخدم / Username:** accountant
- **كلمة المرور / Password:** acc123

## هيكل المشروع / Project Structure

```
KiloAccounting/
├── Models/                 # نماذج البيانات / Data Models
├── Views/                  # واجهات المستخدم / User Interfaces
│   ├── LoginWindow.xaml    # نافذة تسجيل الدخول
│   ├── MainWindow.xaml     # النافذة الرئيسية
│   └── Pages/              # صفحات التطبيق
├── ViewModels/             # نماذج العرض / View Models
├── Services/               # الخدمات / Services
├── Data/                   # طبقة البيانات / Data Layer
├── Resources/              # الموارد / Resources
│   ├── Styles.xaml         # الأنماط
│   ├── Colors.xaml         # الألوان
│   ├── Strings.ar.xaml     # النصوص العربية
│   └── Strings.en.xaml     # النصوص الإنجليزية
└── Helpers/                # المساعدات / Helpers
```

## قاعدة البيانات / Database

يستخدم التطبيق قاعدة بيانات SQLite محلية تُنشأ تلقائياً عند التشغيل الأول. تحتوي على:

- جداول المستخدمين والصلاحيات
- جداول العملاء والموردين
- جداول الأصناف والمخزون
- جداول الفواتير والمعاملات
- جداول الحسابات والقيود المحاسبية

## الأمان / Security

- تشفير كلمات المرور باستخدام BCrypt
- نظام صلاحيات متدرج حسب دور المستخدم
- حماية من SQL Injection باستخدام Entity Framework
- تسجيل العمليات والأنشطة

## التطوير المستقبلي / Future Development

- [ ] إصدار ويب باستخدام ASP.NET Core
- [ ] تطبيق موبايل باستخدام .NET MAUI
- [ ] ربط مع أنظمة دفع إلكترونية
- [ ] تكامل مع البنوك
- [ ] ذكاء اصطناعي للتنبؤات المالية
- [ ] API للتكامل مع أنظمة أخرى

## الدعم والمساهمة / Support & Contribution

للدعم الفني أو المساهمة في التطوير، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- GitHub Issues: [رابط المشاكل]

## الترخيص / License

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

---

© 2025 كيلو للبرمجيات - جميع الحقوق محفوظة
© 2025 Kilo Software Solutions - All Rights Reserved