D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\KiloAccounting.exe
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\KiloAccounting.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\BCrypt.Net-Next.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\MaterialDesignColors.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\MaterialDesignThemes.Wpf.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Data.Sqlite.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Relational.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Sqlite.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.Caching.Abstractions.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.Caching.Memory.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Json.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.Options.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.Primitives.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Xaml.Behaviors.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\SQLitePCLRaw.batteries_v2.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\SQLitePCLRaw.core.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\SQLitePCLRaw.provider.e_sqlite3.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\System.Text.Json.dll
D:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\win-x64\native\e_sqlite3.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\KiloAccounting.deps.json
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\KiloAccounting.runtimeconfig.json
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\KiloAccounting.pdb
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\BouncyCastle.Crypto.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\EntityFramework.SqlServer.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\EntityFramework.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\EPPlus.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\EPPlus.Interfaces.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\EPPlus.System.Drawing.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Humanizer.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\itextsharp.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.EntityFrameworkCore.Design.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyModel.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Microsoft.IO.RecyclableMemoryStream.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Mono.TextTemplating.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\Newtonsoft.Json.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\System.Data.SQLite.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\System.Data.SqlClient.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\System.Data.SQLite.EF6.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\System.Security.Cryptography.Pkcs.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\System.Text.Encodings.Web.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\win-arm64\native\sni.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\win-x64\native\sni.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\win-x86\native\sni.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\alpine-arm\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\alpine-arm64\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\alpine-x64\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\browser-wasm\nativeassets\net6.0\e_sqlite3.a
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\linux-arm\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\linux-arm64\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\linux-armel\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\linux-mips64\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\linux-musl-arm\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\linux-musl-arm64\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\linux-musl-x64\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\linux-ppc64le\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\linux-s390x\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\linux-x64\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\linux-x86\native\libe_sqlite3.so
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\osx-arm64\native\libe_sqlite3.dylib
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\osx-x64\native\libe_sqlite3.dylib
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\win-arm\native\e_sqlite3.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\win-arm64\native\e_sqlite3.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\win-x86\native\e_sqlite3.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\linux-x64\native\SQLite.Interop.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\osx-x64\native\SQLite.Interop.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\win-x64\native\SQLite.Interop.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\win-x86\native\SQLite.Interop.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Security.Cryptography.Pkcs.dll
d:\REACT JS\accounting with kilo\KiloAccounting\bin\Debug\net6.0-windows\runtimes\browser\lib\net6.0\System.Text.Encodings.Web.dll
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\KiloAccounting.csproj.AssemblyReference.cache
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\Views\LoginWindow.baml
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\Views\MainWindow.baml
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\Views\Pages\DashboardPage.baml
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\Views\Pages\SalesInvoicesPage.baml
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\App.baml
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\Views\LoginWindow.g.cs
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\Views\MainWindow.g.cs
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\Views\Pages\DashboardPage.g.cs
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\Views\Pages\SalesInvoicesPage.g.cs
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\App.g.cs
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\KiloAccounting_MarkupCompile.cache
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\KiloAccounting.g.resources
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\KiloAccounting.GeneratedMSBuildEditorConfig.editorconfig
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\KiloAccounting.AssemblyInfoInputs.cache
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\KiloAccounting.AssemblyInfo.cs
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\KiloAccounting.csproj.CoreCompileInputs.cache
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\KiloAcco.********.Up2Date
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\KiloAccounting.dll
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\refint\KiloAccounting.dll
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\KiloAccounting.pdb
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\KiloAccounting.genruntimeconfig.cache
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\ref\KiloAccounting.dll
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\KiloAccounting_MarkupCompile.lref
d:\REACT JS\accounting with kilo\KiloAccounting\obj\Debug\net6.0-windows\GeneratedInternalTypeHelper.g.cs
