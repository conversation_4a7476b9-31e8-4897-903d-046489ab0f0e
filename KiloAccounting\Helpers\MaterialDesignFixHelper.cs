using System;
using System.Windows;
using System.Windows.Data;
using System.Windows.Markup;
using System.Globalization;
using System.Reflection;
using System.Collections.Generic;
using System.Windows.Controls;
using System.Linq;

namespace KiloAccounting.Helpers
{
    /// <summary>
    /// Helper class to fix issues with MaterialDesign converters and markup extensions
    /// </summary>
    public static class MaterialDesign<PERSON>ixHelper
    {
        // Dictionary to store original converters and their replacements
        private static Dictionary<Type, IValueConverter> _converterReplacements = new Dictionary<Type, IValueConverter>();
        
        // Dictionary to store original markup extensions and their replacements
        private static Dictionary<Type, MarkupExtension> _markupExtensionReplacements = new Dictionary<Type, MarkupExtension>();

        /// <summary>
        /// Initializes the fix by registering safe replacements for problematic converters and markup extensions
        /// </summary>
        public static void Initialize()
        {
            try
            {
                // Clear any existing replacements to avoid duplicates
                _converterReplacements.Clear();
                _markupExtensionReplacements.Clear();
                
                // Register our safe converters
                RegisterSafeConverters();
                Console.WriteLine("Safe converters registered");
                
                // Register our safe markup extensions
                RegisterSafeMarkupExtensions();
                Console.WriteLine("Safe markup extensions registered");
                
                // Apply the fixes to the application resources
                ApplyFixes();
                Console.WriteLine("Fixes applied to application resources");
                
                // Ensure the Inverter is available in resources
                if (!Application.Current.Resources.Contains("Inverter"))
                {
                    Application.Current.Resources.Add("Inverter", new InverterMarkupExtension());
                }
                
                Console.WriteLine("MaterialDesignFixHelper initialized successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing MaterialDesignFixHelper: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                
                // Try to add the essential resources even if initialization failed
                try
                {
                    if (Application.Current != null && Application.Current.Resources != null)
                    {
                        if (!Application.Current.Resources.Contains("Inverter"))
                        {
                            Application.Current.Resources.Add("Inverter", new InverterMarkupExtension());
                        }
                        
                        if (!Application.Current.Resources.Contains("inverterMarkupExtension"))
                        {
                            Application.Current.Resources.Add("inverterMarkupExtension", new InverterMarkupExtension());
                        }
                    }
                }
                catch (Exception innerEx)
                {
                    Console.WriteLine($"Error adding essential resources: {innerEx.Message}");
                }
            }
        }

        /// <summary>
        /// Registers safe replacements for problematic converters
        /// </summary>
        private static void RegisterSafeConverters()
        {
            // Register our safe converter as a replacement for any problematic converters
            var safeConverter = new SafeValueConverter();
            
            // Add it to the replacements dictionary with generic types
            _converterReplacements.Add(typeof(IValueConverter), safeConverter);
        }

        /// <summary>
        /// Registers safe replacements for problematic markup extensions
        /// </summary>
        private static void RegisterSafeMarkupExtensions()
        {
            // Register our safe markup extension as a replacement for any problematic markup extensions
            var safeMarkupExtension = new InverterMarkupExtension();
            
            try
            {
                // Add it to the replacements dictionary - we don't know the exact type name, so we'll use a generic approach
                _markupExtensionReplacements.Add(typeof(MarkupExtension), safeMarkupExtension);
                
                // Also add specific types if we can find them
                var materialDesignAssembly = AppDomain.CurrentDomain.GetAssemblies()
                    .FirstOrDefault(a => a.FullName.Contains("MaterialDesignThemes"));
                
                if (materialDesignAssembly != null)
                {
                    // Try to find any inverter markup extension types
                    var inverterTypes = materialDesignAssembly.GetTypes()
                        .Where(t => t.Name.Contains("Inverter") && typeof(MarkupExtension).IsAssignableFrom(t));
                    
                    foreach (var inverterType in inverterTypes)
                    {
                        if (!_markupExtensionReplacements.ContainsKey(inverterType))
                        {
                            _markupExtensionReplacements.Add(inverterType, safeMarkupExtension);
                            Console.WriteLine($"Added replacement for {inverterType.Name}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error registering markup extensions: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies the fixes to the application resources
        /// </summary>
        private static void ApplyFixes()
        {
            try
            {
                if (Application.Current == null || Application.Current.Resources == null)
                    return;
                    
                // Get all resources
                var keys = new List<object>(Application.Current.Resources.Keys.Cast<object>());
                foreach (var key in keys)
                {
                    try
                    {
                        var resource = Application.Current.Resources[key];
                        
                        // Check if it's a converter
                        if (resource is IValueConverter converter)
                        {
                            var converterType = converter.GetType();
                            
                            // Check if we have a replacement for this converter
                            foreach (var replacementType in _converterReplacements.Keys)
                            {
                                if (converterType.Name.Contains(replacementType.Name) || 
                                    replacementType.IsAssignableFrom(converterType))
                                {
                                    // Replace the converter
                                    Application.Current.Resources[key] = _converterReplacements[replacementType];
                                    Console.WriteLine($"Replaced converter: {converterType.Name}");
                                    break;
                                }
                            }
                        }
                        
                        // Check if it's a markup extension
                        if (resource is MarkupExtension markupExtension)
                        {
                            var markupExtensionType = markupExtension.GetType();
                            
                            // Check if we have a replacement for this markup extension
                            foreach (var replacementType in _markupExtensionReplacements.Keys)
                            {
                                if (markupExtensionType.Name.Contains(replacementType.Name) || 
                                    replacementType.IsAssignableFrom(markupExtensionType) ||
                                    markupExtensionType.Name.Contains("Inverter") || 
                                    markupExtensionType.Name.Contains("inverter"))
                                {
                                    // Replace the markup extension
                                    Application.Current.Resources[key] = _markupExtensionReplacements[replacementType];
                                    Console.WriteLine($"Replaced markup extension: {markupExtensionType.Name}");
                                    break;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error processing resource {key}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error applying fixes: {ex.Message}");
            }
            
            // Add our safe converters and markup extensions to the application resources
            if (!Application.Current.Resources.Contains("SafeConverter"))
                Application.Current.Resources.Add("SafeConverter", new SafeValueConverter());
                
            if (!Application.Current.Resources.Contains("Inverter"))
                Application.Current.Resources.Add("Inverter", new InverterMarkupExtension());
        }
    }
}
