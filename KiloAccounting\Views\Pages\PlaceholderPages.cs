using System.Windows.Controls;
using System.Windows;

namespace KiloAccounting.Views.Pages
{
    // Sales Returns Page
    public partial class SalesReturnsPage : Page
    {
        public SalesReturnsPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("مرتجع المبيعات");
        }
    }

    // Purchase Invoices Page
    public partial class PurchaseInvoicesPage : Page
    {
        public PurchaseInvoicesPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("فواتير المشتريات");
        }
    }

    // Purchase Returns Page
    public partial class PurchaseReturnsPage : Page
    {
        public PurchaseReturnsPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("مرتجع المشتريات");
        }
    }

    // Products Page
    public partial class ProductsPage : Page
    {
        public ProductsPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("الأصناف");
        }
    }

    // Inventory Page
    public partial class InventoryPage : Page
    {
        public InventoryPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("إدارة المخزون");
        }
    }

    // Customers Page
    public partial class CustomersPage : Page
    {
        public CustomersPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("العملاء");
        }
    }

    // Suppliers Page
    public partial class SuppliersPage : Page
    {
        public SuppliersPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("الموردين");
        }
    }

    // Chart of Accounts Page
    public partial class ChartOfAccountsPage : Page
    {
        public ChartOfAccountsPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("شجرة الحسابات");
        }
    }

    // Journal Entries Page
    public partial class JournalEntriesPage : Page
    {
        public JournalEntriesPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("القيود اليومية");
        }
    }

    // Financial Reports Page
    public partial class FinancialReportsPage : Page
    {
        public FinancialReportsPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("التقارير المالية");
        }
    }

    // Inventory Reports Page
    public partial class InventoryReportsPage : Page
    {
        public InventoryReportsPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("تقارير المخزون");
        }
    }

    // Users Page
    public partial class UsersPage : Page
    {
        public UsersPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("المستخدمين");
        }
    }

    // Company Settings Page
    public partial class CompanySettingsPage : Page
    {
        public CompanySettingsPage()
        {
            Content = PlaceholderHelper.CreatePlaceholderContent("إعدادات الشركة");
        }
    }

    // Helper class to create placeholder content
    public static class PlaceholderHelper
    {
        public static FrameworkElement CreatePlaceholderContent(string pageTitle)
        {
            var grid = new Grid();
            grid.Margin = new Thickness(20);

            var stackPanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            var titleBlock = new TextBlock
            {
                Text = pageTitle,
                FontSize = 32,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var messageBlock = new TextBlock
            {
                Text = "هذه الصفحة قيد التطوير",
                FontSize = 18,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var detailBlock = new TextBlock
            {
                Text = "سيتم إضافة المحتوى والوظائف قريباً",
                FontSize = 14,
                HorizontalAlignment = HorizontalAlignment.Center,
                Opacity = 0.7
            };

            stackPanel.Children.Add(titleBlock);
            stackPanel.Children.Add(messageBlock);
            stackPanel.Children.Add(detailBlock);

            grid.Children.Add(stackPanel);

            return grid;
        }
    }
}