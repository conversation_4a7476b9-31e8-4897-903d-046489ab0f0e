<Page x:Class="KiloAccounting.Views.Pages.DashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="لوحة التحكم"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <Style x:Key="DashboardCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Effect" Value="{StaticResource MaterialDesignShadowDepth2}"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
        </Style>

        <Style x:Key="CardTitle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="StatNumber" TargetType="TextBlock">
            <Setter Property="FontSize" Value="32"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,10"/>
        </Style>

        <Style x:Key="StatLabel" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
        </Style>
    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Welcome Section -->
            <Border Grid.Row="0" Style="{StaticResource DashboardCard}">
                <StackPanel>
                    <TextBlock Text="مرحباً بك في نظام كيلو للمحاسبة المالية" 
                               FontSize="24" 
                               FontWeight="Bold"
                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,10"/>
                    <TextBlock x:Name="WelcomeMessageTextBlock"
                               Text="أهلاً وسهلاً بك، مدير النظام" 
                               FontSize="16"
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource MaterialDesignBody}"/>
                    <TextBlock x:Name="LastLoginTextBlock"
                               Text="آخر تسجيل دخول: اليوم 08:30 صباحاً" 
                               FontSize="12"
                               HorizontalAlignment="Center"
                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                               Margin="0,5,0,0"/>
                </StackPanel>
            </Border>

            <!-- Statistics Cards -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Sales Today -->
                <Border Grid.Column="0" Style="{StaticResource DashboardCard}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="TrendingUp" 
                                                     Width="24" Height="24" 
                                                     Foreground="Green"
                                                     Margin="0,0,10,0"/>
                            <TextBlock Text="مبيعات اليوم" Style="{StaticResource CardTitle}"/>
                        </StackPanel>
                        <TextBlock x:Name="TodaySalesTextBlock" 
                                   Text="15,750" 
                                   Style="{StaticResource StatNumber}"
                                   Foreground="Green"/>
                        <TextBlock Text="ريال سعودي" Style="{StaticResource StatLabel}"/>
                    </StackPanel>
                </Border>

                <!-- Purchases Today -->
                <Border Grid.Column="1" Style="{StaticResource DashboardCard}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="TrendingDown" 
                                                     Width="24" Height="24" 
                                                     Foreground="Orange"
                                                     Margin="0,0,10,0"/>
                            <TextBlock Text="مشتريات اليوم" Style="{StaticResource CardTitle}"/>
                        </StackPanel>
                        <TextBlock x:Name="TodayPurchasesTextBlock" 
                                   Text="8,500" 
                                   Style="{StaticResource StatNumber}"
                                   Foreground="Orange"/>
                        <TextBlock Text="ريال سعودي" Style="{StaticResource StatLabel}"/>
                    </StackPanel>
                </Border>

                <!-- Total Customers -->
                <Border Grid.Column="2" Style="{StaticResource DashboardCard}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="AccountGroup" 
                                                     Width="24" Height="24" 
                                                     Foreground="Blue"
                                                     Margin="0,0,10,0"/>
                            <TextBlock Text="إجمالي العملاء" Style="{StaticResource CardTitle}"/>
                        </StackPanel>
                        <TextBlock x:Name="TotalCustomersTextBlock" 
                                   Text="127" 
                                   Style="{StaticResource StatNumber}"
                                   Foreground="Blue"/>
                        <TextBlock Text="عميل" Style="{StaticResource StatLabel}"/>
                    </StackPanel>
                </Border>

                <!-- Low Stock Items -->
                <Border Grid.Column="3" Style="{StaticResource DashboardCard}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="AlertCircle" 
                                                     Width="24" Height="24" 
                                                     Foreground="Red"
                                                     Margin="0,0,10,0"/>
                            <TextBlock Text="أصناف منخفضة" Style="{StaticResource CardTitle}"/>
                        </StackPanel>
                        <TextBlock x:Name="LowStockItemsTextBlock" 
                                   Text="5" 
                                   Style="{StaticResource StatNumber}"
                                   Foreground="Red"/>
                        <TextBlock Text="صنف" Style="{StaticResource StatLabel}"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Charts and Recent Activities -->
            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Sales Chart -->
                <Border Grid.Column="0" Style="{StaticResource DashboardCard}">
                    <StackPanel>
                        <TextBlock Text="مبيعات آخر 7 أيام" Style="{StaticResource CardTitle}"/>
                        <Border Height="200" 
                                Background="{DynamicResource MaterialDesignDivider}"
                                CornerRadius="4">
                            <TextBlock Text="مخطط المبيعات سيتم عرضه هنا" 
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Recent Activities -->
                <Border Grid.Column="1" Style="{StaticResource DashboardCard}">
                    <StackPanel>
                        <TextBlock Text="الأنشطة الأخيرة" Style="{StaticResource CardTitle}"/>
                        <ScrollViewer Height="200" VerticalScrollBarVisibility="Auto">
                            <StackPanel>
                                <Border Background="{DynamicResource MaterialDesignDivider}" 
                                        CornerRadius="4" 
                                        Padding="10" 
                                        Margin="0,2">
                                    <StackPanel>
                                        <TextBlock Text="فاتورة مبيعات جديدة #1001" 
                                                   FontWeight="Medium" 
                                                   FontSize="12"/>
                                        <TextBlock Text="منذ 5 دقائق" 
                                                   FontSize="10" 
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </StackPanel>
                                </Border>

                                <Border Background="{DynamicResource MaterialDesignDivider}" 
                                        CornerRadius="4" 
                                        Padding="10" 
                                        Margin="0,2">
                                    <StackPanel>
                                        <TextBlock Text="إضافة عميل جديد: أحمد محمد" 
                                                   FontWeight="Medium" 
                                                   FontSize="12"/>
                                        <TextBlock Text="منذ 15 دقيقة" 
                                                   FontSize="10" 
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </StackPanel>
                                </Border>

                                <Border Background="{DynamicResource MaterialDesignDivider}" 
                                        CornerRadius="4" 
                                        Padding="10" 
                                        Margin="0,2">
                                    <StackPanel>
                                        <TextBlock Text="تحديث مخزون صنف: لابتوب HP" 
                                                   FontWeight="Medium" 
                                                   FontSize="12"/>
                                        <TextBlock Text="منذ 30 دقيقة" 
                                                   FontSize="10" 
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </StackPanel>
                                </Border>

                                <Border Background="{DynamicResource MaterialDesignDivider}" 
                                        CornerRadius="4" 
                                        Padding="10" 
                                        Margin="0,2">
                                    <StackPanel>
                                        <TextBlock Text="دفعة من العميل: شركة النور" 
                                                   FontWeight="Medium" 
                                                   FontSize="12"/>
                                        <TextBlock Text="منذ ساعة" 
                                                   FontSize="10" 
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </ScrollViewer>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Quick Actions -->
            <Border Grid.Row="3" Style="{StaticResource DashboardCard}" Margin="10,10,10,20">
                <StackPanel>
                    <TextBlock Text="إجراءات سريعة" Style="{StaticResource CardTitle}"/>
                    <UniformGrid Columns="4" Rows="2">
                        
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Height="60" Margin="5"
                                Click="QuickAction_Click"
                                Tag="NewSalesInvoice">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Receipt" Width="24" Height="24"/>
                                <TextBlock Text="فاتورة مبيعات" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Height="60" Margin="5"
                                Click="QuickAction_Click"
                                Tag="NewPurchaseInvoice">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="ShoppingCart" Width="24" Height="24"/>
                                <TextBlock Text="فاتورة مشتريات" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Height="60" Margin="5"
                                Click="QuickAction_Click"
                                Tag="NewCustomer">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="AccountPlus" Width="24" Height="24"/>
                                <TextBlock Text="عميل جديد" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Height="60" Margin="5"
                                Click="QuickAction_Click"
                                Tag="NewProduct">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="PackageVariant" Width="24" Height="24"/>
                                <TextBlock Text="صنف جديد" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Height="60" Margin="5"
                                Click="QuickAction_Click"
                                Tag="InventoryReport">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="ChartBox" Width="24" Height="24"/>
                                <TextBlock Text="تقرير المخزون" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Height="60" Margin="5"
                                Click="QuickAction_Click"
                                Tag="FinancialReport">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="ChartLine" Width="24" Height="24"/>
                                <TextBlock Text="تقرير مالي" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Height="60" Margin="5"
                                Click="QuickAction_Click"
                                Tag="Backup">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Backup" Width="24" Height="24"/>
                                <TextBlock Text="نسخ احتياطي" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Height="60" Margin="5"
                                Click="QuickAction_Click"
                                Tag="Settings">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Cog" Width="24" Height="24"/>
                                <TextBlock Text="الإعدادات" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>

                    </UniformGrid>
                </StackPanel>
            </Border>

        </Grid>
    </ScrollViewer>
</Page>