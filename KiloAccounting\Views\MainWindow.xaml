<Window x:Class="KiloAccounting.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:helpers="clr-namespace:KiloAccounting.Helpers"
        Title="نظام كيلو للمحاسبة المالية"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Custom converters -->
        <helpers:SafeValueConverter x:Key="SafeConverter"/>

        <Style x:Key="NavigationButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
            <Setter Property="Height" Value="50"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Padding" Value="20,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource PrimaryHueLightBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="NavigationHeader" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueLightBrush}"/>
            <Setter Property="Margin" Value="20,15,20,5"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Navigation Panel -->
        <Border Grid.Column="0"
                Background="{DynamicResource PrimaryHueMidBrush}"
                Effect="{StaticResource MaterialDesignShadowDepth2}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <Border Grid.Row="0"
                        Background="{DynamicResource PrimaryHueDarkBrush}"
                        Height="80">
                    <StackPanel VerticalAlignment="Center"
                                HorizontalAlignment="Center"
                                Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Calculator"
                                                 Width="32" Height="32"
                                                 Foreground="White"
                                                 Margin="0,0,10,0"/>
                        <StackPanel>
                            <TextBlock Text="كيلو للمحاسبة"
                                       FontSize="18"
                                       FontWeight="Bold"
                                       Foreground="White"/>
                            <TextBlock Text="النظام المحاسبي المتكامل"
                                       FontSize="10"
                                       Foreground="{DynamicResource PrimaryHueLightBrush}"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Navigation Menu -->
                <ScrollViewer Grid.Row="1"
                              VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled">
                    <StackPanel>

                        <!-- Dashboard -->
                        <Button x:Name="DashboardButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="Dashboard">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ViewDashboard"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="لوحة التحكم"/>
                            </StackPanel>
                        </Button>

                        <!-- Sales Section -->
                        <TextBlock Text="المبيعات" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="SalesInvoicesButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="SalesInvoices">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Receipt"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="فواتير المبيعات"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SalesReturnsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="SalesReturns">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ArrowLeft"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="مرتجع المبيعات"/>
                            </StackPanel>
                        </Button>

                        <!-- Purchases Section -->
                        <TextBlock Text="المشتريات" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="PurchaseInvoicesButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="PurchaseInvoices">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ShoppingCart"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="فواتير المشتريات"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="PurchaseReturnsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="PurchaseReturns">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ArrowLeft"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="مرتجع المشتريات"/>
                            </StackPanel>
                        </Button>

                        <!-- Inventory Section -->
                        <TextBlock Text="المخزون" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="ProductsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="Products">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Box"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="الأصناف"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="InventoryButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="Inventory">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Store"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="إدارة المخزون"/>
                            </StackPanel>
                        </Button>

                        <!-- Customers & Suppliers -->
                        <TextBlock Text="العملاء والموردين" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="CustomersButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="Customers">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountGroup"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="العملاء"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SuppliersButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="Suppliers">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Truck"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="الموردين"/>
                            </StackPanel>
                        </Button>

                        <!-- Accounting -->
                        <TextBlock Text="المحاسبة" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="ChartOfAccountsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="ChartOfAccounts">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileTree"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="شجرة الحسابات"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="JournalEntriesButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="JournalEntries">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Book"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="القيود اليومية"/>
                            </StackPanel>
                        </Button>

                        <!-- Reports -->
                        <TextBlock Text="التقارير" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="FinancialReportsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="FinancialReports">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileDocument"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="التقارير المالية"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="InventoryReportsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="InventoryReports">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileDocument"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="تقارير المخزون"/>
                            </StackPanel>
                        </Button>

                        <!-- Settings -->
                        <TextBlock Text="الإعدادات" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="UsersButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="Users">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountMultiple"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="المستخدمين"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="CompanySettingsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="CompanySettings">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cog"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="إعدادات الشركة"/>
                            </StackPanel>
                        </Button>

                    </StackPanel>
                </ScrollViewer>

                <!-- User Info -->
                <Border Grid.Row="2"
                        Background="{DynamicResource PrimaryHueDarkBrush}"
                        Height="60">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0"
                                    VerticalAlignment="Center"
                                    Margin="15,0">
                            <TextBlock x:Name="UserNameTextBlock"
                                       Text="مدير النظام"
                                       FontWeight="Bold"
                                       FontSize="14"
                                       Foreground="White"/>
                            <TextBlock x:Name="UserRoleTextBlock"
                                       Text="مدير"
                                       FontSize="10"
                                       Foreground="{DynamicResource PrimaryHueLightBrush}"/>
                        </StackPanel>

                        <Button Grid.Column="1"
                                Style="{StaticResource MaterialDesignIconButton}"
                                Width="40" Height="40"
                                Margin="10"
                                Click="LogoutButton_Click"
                                ToolTip="تسجيل الخروج">
                            <materialDesign:PackIcon Kind="Logout"
                                                     Foreground="White"/>
                        </Button>
                    </Grid>
                </Border>

            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Top Bar -->
            <Border Grid.Row="0"
                    Background="White"
                    Height="60"
                    Effect="{StaticResource MaterialDesignShadowDepth1}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0"
                                VerticalAlignment="Center"
                                Orientation="Horizontal"
                                Margin="20,0">
                        <TextBlock x:Name="PageTitleTextBlock"
                                   Text="لوحة التحكم"
                                   FontSize="20"
                                   FontWeight="Bold"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1"
                                VerticalAlignment="Center"
                                Orientation="Horizontal"
                                Margin="20,0">

                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                ToolTip="الإشعارات">
                            <materialDesign:PackIcon Kind="Bell"/>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                ToolTip="المساعدة">
                            <materialDesign:PackIcon Kind="Help"/>
                        </Button>

                        <TextBlock x:Name="DateTimeTextBlock"
                                   VerticalAlignment="Center"
                                   Margin="15,0"
                                   FontSize="12"
                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Content Frame -->
            <Frame x:Name="MainContentFrame"
                   Grid.Row="1"
                   NavigationUIVisibility="Hidden"
                   Background="{DynamicResource MaterialDesignPaper}"/>

            <!-- Status Bar -->
            <StatusBar Grid.Row="2"
                       Height="25"
                       Background="{DynamicResource MaterialDesignDivider}">
                <StatusBarItem>
                    <TextBlock x:Name="StatusTextBlock"
                               Text="جاهز"
                               FontSize="11"/>
                </StatusBarItem>
                <Separator/>
                <StatusBarItem>
                    <TextBlock Text="الإصدار 1.0.0"
                               FontSize="11"/>
                </StatusBarItem>
            </StatusBar>

        </Grid>
    </Grid>
</Window>