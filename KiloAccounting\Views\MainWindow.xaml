<Window x:Class="KiloAccounting.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:helpers="clr-namespace:KiloAccounting.Helpers"
        Title="نظام كيلو للمحاسبة المالية"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Custom converters -->
        <helpers:SafeValueConverter x:Key="SafeConverter"/>

        <Style x:Key="NavigationButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
            <Setter Property="Height" Value="50"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="Padding" Value="20,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="Foreground" Value="White"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource PrimaryHueLightBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="NavigationHeader" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueLightBrush}"/>
            <Setter Property="Margin" Value="20,15,20,5"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Modern Navigation Panel -->
        <Border Grid.Column="0"
                Background="{StaticResource CardGradientBrush}"
                CornerRadius="0,20,20,0"
                Margin="0,0,12,0">
            <Border.Effect>
                <DropShadowEffect Color="#667eea" BlurRadius="20" ShadowDepth="0" Opacity="0.15"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Modern Header -->
                <Border Grid.Row="0"
                        Background="{StaticResource PrimaryGradientBrush}"
                        CornerRadius="0,20,0,0"
                        Height="100">
                    <StackPanel VerticalAlignment="Center"
                                HorizontalAlignment="Center"
                                Orientation="Vertical">
                        <materialDesign:PackIcon Kind="Calculator"
                                                 Width="40" Height="40"
                                                 Foreground="White"
                                                 Margin="0,0,0,8"/>
                        <TextBlock Text="كيلو للمحاسبة"
                                   FontSize="18"
                                   FontWeight="Bold"
                                   Foreground="White"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="النظام المحاسبي المتكامل"
                                   FontSize="11"
                                   Foreground="White"
                                   Opacity="0.9"
                                   HorizontalAlignment="Center"
                                   Margin="0,2,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Navigation Menu -->
                <ScrollViewer Grid.Row="1"
                              VerticalScrollBarVisibility="Auto"
                              HorizontalScrollBarVisibility="Disabled">
                    <StackPanel>

                        <!-- Dashboard -->
                        <Button x:Name="DashboardButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="Dashboard">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ViewDashboard"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="لوحة التحكم"/>
                            </StackPanel>
                        </Button>

                        <!-- Sales Section -->
                        <TextBlock Text="المبيعات" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="SalesInvoicesButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="SalesInvoices">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Receipt"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="فواتير المبيعات"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SalesReturnsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="SalesReturns">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ArrowLeft"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="مرتجع المبيعات"/>
                            </StackPanel>
                        </Button>

                        <!-- Purchases Section -->
                        <TextBlock Text="المشتريات" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="PurchaseInvoicesButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="PurchaseInvoices">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ShoppingCart"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="فواتير المشتريات"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="PurchaseReturnsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="PurchaseReturns">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ArrowLeft"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="مرتجع المشتريات"/>
                            </StackPanel>
                        </Button>

                        <!-- Inventory Section -->
                        <TextBlock Text="المخزون" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="ProductsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="Products">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Box"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="الأصناف"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="InventoryButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="Inventory">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Store"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="إدارة المخزون"/>
                            </StackPanel>
                        </Button>

                        <!-- Customers & Suppliers -->
                        <TextBlock Text="العملاء والموردين" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="CustomersButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="Customers">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountGroup"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="العملاء"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SuppliersButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="Suppliers">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Truck"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="الموردين"/>
                            </StackPanel>
                        </Button>

                        <!-- Accounting -->
                        <TextBlock Text="المحاسبة" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="ChartOfAccountsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="ChartOfAccounts">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileTree"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="شجرة الحسابات"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="JournalEntriesButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="JournalEntries">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Book"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="القيود اليومية"/>
                            </StackPanel>
                        </Button>

                        <!-- Reports -->
                        <TextBlock Text="التقارير" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="FinancialReportsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="FinancialReports">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileDocument"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="التقارير المالية"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="InventoryReportsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="InventoryReports">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileDocument"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="تقارير المخزون"/>
                            </StackPanel>
                        </Button>

                        <!-- Settings -->
                        <TextBlock Text="الإعدادات" Style="{StaticResource NavigationHeader}"/>

                        <Button x:Name="UsersButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="Users">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountMultiple"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="المستخدمين"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="CompanySettingsButton"
                                Style="{StaticResource NavigationButton}"
                                Click="NavigationButton_Click"
                                Tag="CompanySettings">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cog"
                                                         Width="20" Height="20"
                                                         Margin="0,0,15,0"/>
                                <TextBlock Text="إعدادات الشركة"/>
                            </StackPanel>
                        </Button>

                    </StackPanel>
                </ScrollViewer>

                <!-- Modern User Info -->
                <Border Grid.Row="2"
                        Background="{StaticResource SecondaryGradientBrush}"
                        CornerRadius="0,0,20,0"
                        Height="80">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- User Avatar -->
                        <Border Grid.Column="0"
                                Width="48" Height="48"
                                CornerRadius="24"
                                Background="{StaticResource PrimaryBrush}"
                                Margin="16,0,12,0"
                                VerticalAlignment="Center">
                            <materialDesign:PackIcon Kind="Account"
                                                     Width="28" Height="28"
                                                     Foreground="White"
                                                     HorizontalAlignment="Center"
                                                     VerticalAlignment="Center"/>
                        </Border>

                        <StackPanel Grid.Column="1"
                                    VerticalAlignment="Center">
                            <TextBlock x:Name="UserNameTextBlock"
                                       Text="مدير النظام"
                                       FontWeight="Bold"
                                       FontSize="14"
                                       Foreground="{StaticResource PrimaryTextBrush}"/>
                            <TextBlock x:Name="UserRoleTextBlock"
                                       Text="مدير"
                                       FontSize="11"
                                       Foreground="{StaticResource SecondaryTextBrush}"/>
                        </StackPanel>

                        <Button Grid.Column="2"
                                Style="{StaticResource IconButton}"
                                Width="40" Height="40"
                                Margin="8,0,16,0"
                                Click="LogoutButton_Click"
                                ToolTip="تسجيل الخروج"
                                Background="Transparent">
                            <materialDesign:PackIcon Kind="Logout"
                                                     Foreground="{StaticResource PrimaryTextBrush}"
                                                     Width="20" Height="20"/>
                        </Button>
                    </Grid>
                </Border>

            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Modern Top Bar -->
            <Border Grid.Row="0"
                    Background="{StaticResource SurfaceBrush}"
                    Height="80"
                    CornerRadius="16,16,0,0"
                    Margin="0,8,8,0">
                <Border.Effect>
                    <DropShadowEffect Color="#667eea" BlurRadius="15" ShadowDepth="0" Opacity="0.1"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0"
                                VerticalAlignment="Center"
                                Orientation="Horizontal"
                                Margin="24,0">
                        <Border Background="{StaticResource PrimaryGradientBrush}"
                                CornerRadius="8"
                                Padding="12,6"
                                Margin="0,0,16,0">
                            <materialDesign:PackIcon Kind="ViewDashboard"
                                                     Width="20" Height="20"
                                                     Foreground="White"/>
                        </Border>
                        <TextBlock x:Name="PageTitleTextBlock"
                                   Text="لوحة التحكم"
                                   FontSize="24"
                                   FontWeight="Bold"
                                   Foreground="{StaticResource PrimaryTextBrush}"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1"
                                VerticalAlignment="Center"
                                Orientation="Horizontal"
                                Margin="24,0">

                        <!-- Notifications Button -->
                        <Button Style="{StaticResource IconButton}"
                                ToolTip="الإشعارات"
                                Margin="4">
                            <Grid>
                                <materialDesign:PackIcon Kind="Bell"
                                                       Width="20" Height="20"
                                                       Foreground="{StaticResource PrimaryTextBrush}"/>
                                <!-- Notification Badge -->
                                <Border Background="{StaticResource ErrorBrush}"
                                        CornerRadius="8"
                                        Width="16" Height="16"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Top"
                                        Margin="0,-4,-4,0">
                                    <TextBlock Text="3"
                                               FontSize="10"
                                               FontWeight="Bold"
                                               Foreground="White"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                </Border>
                            </Grid>
                        </Button>

                        <!-- Help Button -->
                        <Button Style="{StaticResource IconButton}"
                                ToolTip="المساعدة"
                                Margin="4">
                            <materialDesign:PackIcon Kind="Help"
                                                   Width="20" Height="20"
                                                   Foreground="{StaticResource PrimaryTextBrush}"/>
                        </Button>

                        <!-- Date Time Display -->
                        <Border Background="{StaticResource LightGrayBrush}"
                                CornerRadius="12"
                                Padding="16,8"
                                Margin="8,0,0,0">
                            <StackPanel Orientation="Vertical">
                                <TextBlock x:Name="DateTimeTextBlock"
                                           FontSize="12"
                                           FontWeight="SemiBold"
                                           Foreground="{StaticResource PrimaryTextBrush}"
                                           HorizontalAlignment="Center"/>
                                <TextBlock Text="اليوم"
                                           FontSize="10"
                                           Foreground="{StaticResource SecondaryTextBrush}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Modern Content Frame -->
            <Border Grid.Row="1"
                    Background="{StaticResource BackgroundBrush}"
                    CornerRadius="0,0,16,0"
                    Margin="0,0,8,8">
                <Frame x:Name="MainContentFrame"
                       NavigationUIVisibility="Hidden"
                       Background="Transparent"/>
            </Border>

            <!-- Modern Status Bar -->
            <Border Grid.Row="2"
                    Background="{StaticResource SurfaceBrush}"
                    Height="40"
                    CornerRadius="0,0,16,0"
                    Margin="0,0,8,8">
                <Border.Effect>
                    <DropShadowEffect Color="#667eea" BlurRadius="10" ShadowDepth="0" Opacity="0.05"/>
                </Border.Effect>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Status Text -->
                    <StackPanel Grid.Column="0"
                                Orientation="Horizontal"
                                VerticalAlignment="Center"
                                Margin="20,0">
                        <Border Background="{StaticResource SuccessBrush}"
                                CornerRadius="6"
                                Width="12" Height="12"
                                Margin="0,0,8,0"/>
                        <TextBlock x:Name="StatusTextBlock"
                                   Text="جاهز"
                                   FontSize="12"
                                   FontWeight="Medium"
                                   Foreground="{StaticResource PrimaryTextBrush}"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Version Info -->
                    <Border Grid.Column="1"
                            Background="{StaticResource LightGrayBrush}"
                            CornerRadius="8"
                            Padding="12,4"
                            Margin="8,0">
                        <TextBlock Text="الإصدار 1.0.0"
                                   FontSize="11"
                                   FontWeight="Medium"
                                   Foreground="{StaticResource SecondaryTextBrush}"/>
                    </Border>

                    <!-- Connection Status -->
                    <StackPanel Grid.Column="2"
                                Orientation="Horizontal"
                                VerticalAlignment="Center"
                                Margin="0,0,20,0">
                        <materialDesign:PackIcon Kind="Database"
                                               Width="16" Height="16"
                                               Foreground="{StaticResource SuccessBrush}"
                                               Margin="0,0,4,0"/>
                        <TextBlock Text="متصل"
                                   FontSize="11"
                                   FontWeight="Medium"
                                   Foreground="{StaticResource SuccessBrush}"/>
                    </StackPanel>
                </Grid>
            </Border>

        </Grid>
    </Grid>
</Window>