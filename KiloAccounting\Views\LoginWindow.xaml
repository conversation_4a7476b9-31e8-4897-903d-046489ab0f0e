<Window x:Class="KiloAccounting.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام كيلو للمحاسبة"
        Height="600" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- Modern Login Button -->
        <Style x:Key="LoginButton" TargetType="Button">
            <Setter Property="Height" Value="56"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,20,0,10"/>
            <Setter Property="Background" Value="{StaticResource PrimaryGradientBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="28"
                                BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#667eea" BlurRadius="20" ShadowDepth="0" Opacity="0.5"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Login TextBox -->
        <Style x:Key="LoginTextBox" TargetType="TextBox">
            <Setter Property="Height" Value="56"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,12"/>
            <Setter Property="Padding" Value="20,16"/>
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="16">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Focusable="false"
                                        HorizontalScrollBarVisibility="Hidden"
                                        VerticalScrollBarVisibility="Hidden"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#667eea" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Login PasswordBox -->
        <Style x:Key="LoginPasswordBox" TargetType="PasswordBox">
            <Setter Property="Height" Value="56"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,12"/>
            <Setter Property="Padding" Value="20,16"/>
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="PasswordBox">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="16">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Focusable="false"
                                        HorizontalScrollBarVisibility="Hidden"
                                        VerticalScrollBarVisibility="Hidden"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                                <Setter TargetName="border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#667eea" BlurRadius="15" ShadowDepth="0" Opacity="0.3"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="{StaticResource CardGradientBrush}"
            CornerRadius="24">
        <Border.Effect>
            <DropShadowEffect Color="#667eea" Direction="315" ShadowDepth="0" BlurRadius="30" Opacity="0.3"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Modern Header with Gradient -->
            <Border Grid.Row="0"
                    Background="{StaticResource PrimaryGradientBrush}"
                    CornerRadius="24,24,0,0"
                    Height="120">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0"
                                VerticalAlignment="Center"
                                HorizontalAlignment="Center"
                                Orientation="Vertical">
                        <materialDesign:PackIcon Kind="Calculator"
                                                 Width="48" Height="48"
                                                 Foreground="White"
                                                 Margin="0,0,0,8"/>
                        <TextBlock Text="نظام كيلو للمحاسبة"
                                   FontSize="22"
                                   FontWeight="Bold"
                                   Foreground="White"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="نظام محاسبة متطور وعصري"
                                   FontSize="13"
                                   Foreground="White"
                                   Opacity="0.9"
                                   HorizontalAlignment="Center"
                                   Margin="0,4,0,0"/>
                    </StackPanel>

                    <Button Grid.Column="1"
                            Style="{StaticResource IconButton}"
                            Width="48" Height="48"
                            Margin="16"
                            Click="CloseButton_Click"
                            Background="Transparent">
                        <materialDesign:PackIcon Kind="Close"
                                                 Foreground="White"
                                                 Width="24" Height="24"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Login Form -->
            <StackPanel Grid.Row="1"
                        Margin="40,30"
                        VerticalAlignment="Center">

                <TextBlock Text="تسجيل الدخول"
                           FontSize="24"
                           FontWeight="Bold"
                           HorizontalAlignment="Center"
                           Foreground="#212121"
                           Margin="0,0,0,30"/>

                <TextBox x:Name="UsernameTextBox"
                         Style="{StaticResource LoginTextBox}"
                         materialDesign:HintAssist.Hint="اسم المستخدم"
                         Text="admin"/>

                <PasswordBox x:Name="PasswordBox"
                             Style="{StaticResource LoginPasswordBox}"
                             materialDesign:HintAssist.Hint="كلمة المرور"
                             Password="admin123"/>

                <CheckBox x:Name="RememberMeCheckBox"
                          Content="تذكرني"
                          Margin="0,15,0,0"
                          Style="{StaticResource MaterialDesignCheckBox}"/>

                <Button x:Name="LoginButton"
                        Content="دخول"
                        Style="{StaticResource LoginButton}"
                        Click="LoginButton_Click"
                        IsDefault="True"/>

                <TextBlock x:Name="ErrorMessageTextBlock"
                           Foreground="Red"
                           FontSize="12"
                           HorizontalAlignment="Center"
                           Margin="0,10,0,0"
                           Visibility="Collapsed"/>

                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Center"
                            Margin="0,20,0,0">
                    <TextBlock Text="نسيت كلمة المرور؟"
                               FontSize="12"
                               Margin="0,0,5,0"/>
                    <Button Content="إعادة تعيين"
                            Style="{StaticResource MaterialDesignFlatButton}"
                            FontSize="12"
                            Padding="5,0"
                            Click="ResetPasswordButton_Click"/>
                </StackPanel>

            </StackPanel>

            <!-- Footer -->
            <Border Grid.Row="2"
                    Background="#F5F5F5"
                    Height="50">
                <StackPanel VerticalAlignment="Center"
                            HorizontalAlignment="Center"
                            Orientation="Horizontal">
                    <TextBlock Text="© 2025 كيلو للبرمجيات - جميع الحقوق محفوظة"
                               FontSize="10"
                               Foreground="#757575"/>
                </StackPanel>
            </Border>

        </Grid>
    </Border>
</Window>