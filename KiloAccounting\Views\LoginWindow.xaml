<Window x:Class="KiloAccounting.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام كيلو للمحاسبة"
        Height="600" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <Style x:Key="LoginButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Margin" Value="0,10"/>
        </Style>

        <Style x:Key="LoginTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
            <Setter Property="Height" Value="50"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,10"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>

        <Style x:Key="LoginPasswordBox" TargetType="PasswordBox" BasedOn="{StaticResource MaterialDesignPasswordBox}">
            <Setter Property="Height" Value="50"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,10"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>
    </Window.Resources>

    <Border Background="White"
            CornerRadius="15">
        <Border.Effect>
            <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="5" Opacity="0.3"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0"
                    Background="#2196F3"
                    CornerRadius="15,15,0,0"
                    Height="80">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" 
                                VerticalAlignment="Center" 
                                HorizontalAlignment="Center"
                                Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Calculator" 
                                                 Width="32" Height="32" 
                                                 Foreground="White" 
                                                 Margin="0,0,10,0"/>
                        <TextBlock Text="نظام كيلو للمحاسبة" 
                                   FontSize="20" 
                                   FontWeight="Bold" 
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <Button Grid.Column="1" 
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="30" Height="30"
                            Margin="10"
                            Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="Close" 
                                                 Foreground="White"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Login Form -->
            <StackPanel Grid.Row="1" 
                        Margin="40,30"
                        VerticalAlignment="Center">

                <TextBlock Text="تسجيل الدخول" 
                           FontSize="24" 
                           FontWeight="Bold" 
                           HorizontalAlignment="Center"
                           Foreground="#212121"
                           Margin="0,0,0,30"/>

                <TextBox x:Name="UsernameTextBox"
                         Style="{StaticResource LoginTextBox}"
                         materialDesign:HintAssist.Hint="اسم المستخدم"
                         Text="admin"/>

                <PasswordBox x:Name="PasswordBox"
                             Style="{StaticResource LoginPasswordBox}"
                             materialDesign:HintAssist.Hint="كلمة المرور"
                             Password="admin123"/>

                <CheckBox x:Name="RememberMeCheckBox"
                          Content="تذكرني"
                          Margin="0,15,0,0"
                          Style="{StaticResource MaterialDesignCheckBox}"/>

                <Button x:Name="LoginButton"
                        Content="دخول"
                        Style="{StaticResource LoginButton}"
                        Click="LoginButton_Click"
                        IsDefault="True"/>

                <TextBlock x:Name="ErrorMessageTextBlock"
                           Foreground="Red"
                           FontSize="12"
                           HorizontalAlignment="Center"
                           Margin="0,10,0,0"
                           Visibility="Collapsed"/>

                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center"
                            Margin="0,20,0,0">
                    <TextBlock Text="نسيت كلمة المرور؟" 
                               FontSize="12"
                               Margin="0,0,5,0"/>
                    <Button Content="إعادة تعيين"
                            Style="{StaticResource MaterialDesignFlatButton}"
                            FontSize="12"
                            Padding="5,0"
                            Click="ResetPasswordButton_Click"/>
                </StackPanel>

            </StackPanel>

            <!-- Footer -->
            <Border Grid.Row="2"
                    Background="#F5F5F5"
                    Height="50">
                <StackPanel VerticalAlignment="Center"
                            HorizontalAlignment="Center"
                            Orientation="Horizontal">
                    <TextBlock Text="© 2025 كيلو للبرمجيات - جميع الحقوق محفوظة"
                               FontSize="10"
                               Foreground="#757575"/>
                </StackPanel>
            </Border>

        </Grid>
    </Border>
</Window>