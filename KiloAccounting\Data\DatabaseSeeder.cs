using KiloAccounting.Models;
using Microsoft.EntityFrameworkCore;
using BCrypt.Net;
using System;
using System.Collections.Generic;
using System.Linq;

namespace KiloAccounting.Data
{
    public static class DatabaseSeeder
    {
        public static void SeedData(AccountingDbContext context)
        {
            try
            {
                // Ensure database is created
                context.Database.EnsureCreated();

                // Seed Users if none exist
                if (!context.Users.Any())
                {
                    var users = new List<User>
                    {
                        new User
                        {
                            Username = "admin",
                            PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                            FullName = "مدير النظام",
                            Email = "<EMAIL>",
                            Role = UserRole.Manager,
                            IsActive = true,
                            CreatedDate = DateTime.Now,
                            LastLoginDate = null
                        },
                        new User
                        {
                            Username = "accountant",
                            PasswordHash = BCrypt.Net.BCrypt.HashPassword("acc123"),
                            FullName = "المحاسب الرئيسي",
                            Email = "<EMAIL>",
                            Role = UserRole.Accountant,
                            IsActive = true,
                            CreatedDate = DateTime.Now,
                            LastLoginDate = null
                        },
                        new User
                        {
                            Username = "sales",
                            PasswordHash = BCrypt.Net.BCrypt.HashPassword("sales123"),
                            FullName = "موظف المبيعات",
                            Email = "<EMAIL>",
                            Role = UserRole.SalesEmployee,
                            IsActive = true,
                            CreatedDate = DateTime.Now,
                            LastLoginDate = null
                        }
                    };

                    context.Users.AddRange(users);
                    context.SaveChanges();
                }

                Console.WriteLine("Database seeded successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error seeding database: {ex.Message}");
                throw;
            }
        }
    }
}