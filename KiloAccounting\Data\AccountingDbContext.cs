using Microsoft.EntityFrameworkCore;
using KiloAccounting.Models;
using System;
using System.IO;

namespace KiloAccounting.Data
{
    public class AccountingDbContext : DbContext
    {
        public DbSet<User> Users { get; set; }
        public DbSet<Company> Companies { get; set; }
        public DbSet<Account> Accounts { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<CustomerTransaction> CustomerTransactions { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<SupplierTransaction> SupplierTransactions { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<ProductCategory> ProductCategories { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }
        public DbSet<StockMovement> StockMovements { get; set; }
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<InvoiceItem> InvoiceItems { get; set; }
        public DbSet<InvoicePayment> InvoicePayments { get; set; }
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<PurchaseInvoiceItem> PurchaseInvoiceItems { get; set; }
        public DbSet<PurchaseInvoicePayment> PurchaseInvoicePayments { get; set; }
        public DbSet<JournalEntry> JournalEntries { get; set; }
        public DbSet<JournalEntryLine> JournalEntryLines { get; set; }
        public DbSet<Partner> Partners { get; set; }
        public DbSet<PartnerTransaction> PartnerTransactions { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "KiloAccounting.db");
                var directory = Path.GetDirectoryName(dbPath);
                
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                optionsBuilder.UseSqlite($"Data Source={dbPath}");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Role).HasConversion<int>();
            });

            // Configure Account entity
            modelBuilder.Entity<Account>(entity =>
            {
                entity.HasIndex(e => e.AccountCode).IsUnique();
                entity.Property(e => e.AccountType).HasConversion<int>();
                entity.Property(e => e.Category).HasConversion<int>();
                entity.Property(e => e.BalanceType).HasConversion<int>();
                
                entity.HasOne(e => e.ParentAccount)
                    .WithMany(e => e.SubAccounts)
                    .HasForeignKey(e => e.ParentAccountId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Customer entity
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasIndex(e => e.CustomerCode).IsUnique();
                entity.Property(e => e.CustomerType).HasConversion<int>();
                entity.Property(e => e.BalanceType).HasConversion<int>();
                
                entity.HasOne(e => e.Account)
                    .WithMany(e => e.Customers)
                    .HasForeignKey(e => e.AccountId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure Supplier entity
            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.HasIndex(e => e.SupplierCode).IsUnique();
                entity.Property(e => e.SupplierType).HasConversion<int>();
                entity.Property(e => e.BalanceType).HasConversion<int>();
                
                entity.HasOne(e => e.Account)
                    .WithMany(e => e.Suppliers)
                    .HasForeignKey(e => e.AccountId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure Product entity
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasIndex(e => e.ProductCode).IsUnique();
                entity.HasIndex(e => e.Barcode).IsUnique();
                
                entity.HasOne(e => e.Category)
                    .WithMany(e => e.Products)
                    .HasForeignKey(e => e.CategoryId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.Unit)
                    .WithMany(e => e.Products)
                    .HasForeignKey(e => e.UnitId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure ProductCategory entity
            modelBuilder.Entity<ProductCategory>(entity =>
            {
                entity.HasIndex(e => e.CategoryCode).IsUnique();
                
                entity.HasOne(e => e.ParentCategory)
                    .WithMany(e => e.SubCategories)
                    .HasForeignKey(e => e.ParentCategoryId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Unit entity
            modelBuilder.Entity<Unit>(entity =>
            {
                entity.HasIndex(e => e.UnitCode).IsUnique();
            });

            // Configure Invoice entity
            modelBuilder.Entity<Invoice>(entity =>
            {
                entity.HasIndex(e => e.InvoiceNumber).IsUnique();
                entity.Property(e => e.InvoiceType).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.PaymentMethod).HasConversion<int>();
                
                entity.HasOne(e => e.Customer)
                    .WithMany(e => e.Invoices)
                    .HasForeignKey(e => e.CustomerId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure PurchaseInvoice entity
            modelBuilder.Entity<PurchaseInvoice>(entity =>
            {
                entity.HasIndex(e => e.InvoiceNumber).IsUnique();
                entity.Property(e => e.InvoiceType).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.PaymentMethod).HasConversion<int>();
                
                entity.HasOne(e => e.Supplier)
                    .WithMany(e => e.PurchaseInvoices)
                    .HasForeignKey(e => e.SupplierId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure JournalEntry entity
            modelBuilder.Entity<JournalEntry>(entity =>
            {
                entity.HasIndex(e => e.EntryNumber).IsUnique();
                entity.Property(e => e.EntryType).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<int>();
            });

            // Configure JournalEntryLine entity
            modelBuilder.Entity<JournalEntryLine>(entity =>
            {
                entity.HasOne(e => e.JournalEntry)
                    .WithMany(e => e.JournalEntryLines)
                    .HasForeignKey(e => e.JournalEntryId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.Account)
                    .WithMany(e => e.JournalEntryLines)
                    .HasForeignKey(e => e.AccountId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure CustomerTransaction entity
            modelBuilder.Entity<CustomerTransaction>(entity =>
            {
                entity.Property(e => e.TransactionType).HasConversion<int>();
                
                entity.HasOne(e => e.Customer)
                    .WithMany(e => e.Transactions)
                    .HasForeignKey(e => e.CustomerId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.Invoice)
                    .WithMany(e => e.CustomerTransactions)
                    .HasForeignKey(e => e.InvoiceId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure SupplierTransaction entity
            modelBuilder.Entity<SupplierTransaction>(entity =>
            {
                entity.Property(e => e.TransactionType).HasConversion<int>();
                
                entity.HasOne(e => e.Supplier)
                    .WithMany(e => e.Transactions)
                    .HasForeignKey(e => e.SupplierId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.PurchaseInvoice)
                    .WithMany(e => e.SupplierTransactions)
                    .HasForeignKey(e => e.PurchaseInvoiceId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure InventoryTransaction entity
            modelBuilder.Entity<InventoryTransaction>(entity =>
            {
                entity.Property(e => e.TransactionType).HasConversion<int>();
                
                entity.HasOne(e => e.Product)
                    .WithMany(e => e.InventoryTransactions)
                    .HasForeignKey(e => e.ProductId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure StockMovement entity
            modelBuilder.Entity<StockMovement>(entity =>
            {
                entity.Property(e => e.MovementType).HasConversion<int>();
                
                entity.HasOne(e => e.Product)
                    .WithMany(e => e.StockMovements)
                    .HasForeignKey(e => e.ProductId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure Partner entity
            modelBuilder.Entity<Partner>(entity =>
            {
                entity.HasMany(e => e.Transactions)
                    .WithOne(e => e.Partner)
                    .HasForeignKey(e => e.PartnerId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure PartnerTransaction entity
            modelBuilder.Entity<PartnerTransaction>(entity =>
            {
                entity.Property(e => e.TransactionType).HasConversion<int>();
                
                entity.HasOne(e => e.JournalEntry)
                    .WithMany()
                    .HasForeignKey(e => e.JournalEntryId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure InvoiceItem entity
            modelBuilder.Entity<InvoiceItem>(entity =>
            {
                entity.HasOne(e => e.Invoice)
                    .WithMany(e => e.InvoiceItems)
                    .HasForeignKey(e => e.InvoiceId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.Product)
                    .WithMany(e => e.InvoiceItems)
                    .HasForeignKey(e => e.ProductId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure PurchaseInvoiceItem entity
            modelBuilder.Entity<PurchaseInvoiceItem>(entity =>
            {
                entity.HasOne(e => e.PurchaseInvoice)
                    .WithMany(e => e.PurchaseInvoiceItems)
                    .HasForeignKey(e => e.PurchaseInvoiceId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.Product)
                    .WithMany(e => e.PurchaseInvoiceItems)
                    .HasForeignKey(e => e.ProductId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure InvoicePayment entity
            modelBuilder.Entity<InvoicePayment>(entity =>
            {
                entity.Property(e => e.PaymentMethod).HasConversion<int>();
                
                entity.HasOne(e => e.Invoice)
                    .WithMany(e => e.Payments)
                    .HasForeignKey(e => e.InvoiceId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure PurchaseInvoicePayment entity
            modelBuilder.Entity<PurchaseInvoicePayment>(entity =>
            {
                entity.Property(e => e.PaymentMethod).HasConversion<int>();
                
                entity.HasOne(e => e.PurchaseInvoice)
                    .WithMany(e => e.Payments)
                    .HasForeignKey(e => e.PurchaseInvoiceId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }
    }
}