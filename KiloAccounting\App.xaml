<Application x:Class="KiloAccounting.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:helpers="clr-namespace:KiloAccounting.Helpers"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Cyan" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary Source="Resources/Styles.xaml" />
                <ResourceDictionary Source="Resources/Colors.xaml" />
                
                <!-- Localization Resources - Default to Arabic -->
                <ResourceDictionary Source="Resources/Strings.ar.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Custom Converters -->
            <helpers:SafeValueConverter x:Key="SafeValueConverter"/>
            <helpers:InverterMarkupExtension x:Key="Inverter"/>

            <!-- Global Application Styles -->
            <Style x:Key="ModernButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Height" Value="40"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="FontWeight" Value="Medium"/>
            </Style>

            <Style x:Key="ModernTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
                <Setter Property="Height" Value="40"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
            </Style>

            <Style x:Key="ModernComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignComboBox}">
                <Setter Property="Height" Value="40"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
            </Style>

            <Style x:Key="HeaderText" TargetType="TextBlock">
                <Setter Property="FontSize" Value="24"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
                <Setter Property="Margin" Value="0,0,0,20"/>
            </Style>

            <Style x:Key="SubHeaderText" TargetType="TextBlock">
                <Setter Property="FontSize" Value="18"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                <Setter Property="Margin" Value="0,0,0,10"/>
            </Style>

        </ResourceDictionary>
    </Application.Resources>
</Application>
