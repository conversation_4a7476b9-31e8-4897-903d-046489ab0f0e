using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using KiloAccounting.Models;
using KiloAccounting.Views.Pages;

namespace KiloAccounting.Views
{
    public partial class MainWindow : Window
    {
        private User _currentUser;
        private DispatcherTimer _timer;

        public MainWindow()
        {
            InitializeComponent();
            InitializeWindow();
            LoadDashboard();
        }

        private void InitializeWindow()
        {
            // Get current user
            _currentUser = Application.Current.Properties["CurrentUser"] as User;
            
            if (_currentUser != null)
            {
                UserNameTextBlock.Text = _currentUser.FullName;
                UserRoleTextBlock.Text = GetRoleDisplayName(_currentUser.Role);
            }

            // Initialize timer for date/time display
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();

            // Set initial status
            StatusTextBlock.Text = "جاهز";

            // Apply user permissions
            ApplyUserPermissions();
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            DateTimeTextBlock.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss");
        }

        private string GetRoleDisplayName(UserRole role)
        {
            return role switch
            {
                UserRole.Manager => "مدير",
                UserRole.Accountant => "محاسب",
                UserRole.SalesEmployee => "موظف مبيعات",
                UserRole.PurchaseEmployee => "موظف مشتريات",
                UserRole.InventoryEmployee => "موظف مخزون",
                UserRole.Viewer => "مستعلم",
                _ => "غير محدد"
            };
        }

        private void ApplyUserPermissions()
        {
            if (_currentUser == null) return;

            // Hide/show menu items based on user role
            switch (_currentUser.Role)
            {
                case UserRole.SalesEmployee:
                    PurchaseInvoicesButton.Visibility = Visibility.Collapsed;
                    PurchaseReturnsButton.Visibility = Visibility.Collapsed;
                    ChartOfAccountsButton.Visibility = Visibility.Collapsed;
                    JournalEntriesButton.Visibility = Visibility.Collapsed;
                    UsersButton.Visibility = Visibility.Collapsed;
                    CompanySettingsButton.Visibility = Visibility.Collapsed;
                    break;

                case UserRole.PurchaseEmployee:
                    SalesInvoicesButton.Visibility = Visibility.Collapsed;
                    SalesReturnsButton.Visibility = Visibility.Collapsed;
                    ChartOfAccountsButton.Visibility = Visibility.Collapsed;
                    JournalEntriesButton.Visibility = Visibility.Collapsed;
                    UsersButton.Visibility = Visibility.Collapsed;
                    CompanySettingsButton.Visibility = Visibility.Collapsed;
                    break;

                case UserRole.InventoryEmployee:
                    SalesInvoicesButton.Visibility = Visibility.Collapsed;
                    SalesReturnsButton.Visibility = Visibility.Collapsed;
                    PurchaseInvoicesButton.Visibility = Visibility.Collapsed;
                    PurchaseReturnsButton.Visibility = Visibility.Collapsed;
                    ChartOfAccountsButton.Visibility = Visibility.Collapsed;
                    JournalEntriesButton.Visibility = Visibility.Collapsed;
                    FinancialReportsButton.Visibility = Visibility.Collapsed;
                    UsersButton.Visibility = Visibility.Collapsed;
                    CompanySettingsButton.Visibility = Visibility.Collapsed;
                    break;

                case UserRole.Viewer:
                    // Viewers can only see reports and data, no editing
                    SalesInvoicesButton.IsEnabled = false;
                    SalesReturnsButton.IsEnabled = false;
                    PurchaseInvoicesButton.IsEnabled = false;
                    PurchaseReturnsButton.IsEnabled = false;
                    ProductsButton.IsEnabled = false;
                    InventoryButton.IsEnabled = false;
                    CustomersButton.IsEnabled = false;
                    SuppliersButton.IsEnabled = false;
                    ChartOfAccountsButton.IsEnabled = false;
                    JournalEntriesButton.IsEnabled = false;
                    UsersButton.Visibility = Visibility.Collapsed;
                    CompanySettingsButton.Visibility = Visibility.Collapsed;
                    break;

                case UserRole.Accountant:
                    // Accountants have access to most features except user management
                    UsersButton.Visibility = Visibility.Collapsed;
                    break;

                case UserRole.Manager:
                    // Managers have full access
                    break;
            }
        }

        private void NavigationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string pageTag)
            {
                NavigateToPage(pageTag);
                UpdatePageTitle(pageTag);
                StatusTextBlock.Text = $"تم الانتقال إلى {GetPageDisplayName(pageTag)}";
            }
        }

        private void NavigateToPage(string pageTag)
        {
            Page page = pageTag switch
            {
                "Dashboard" => new DashboardPage(),
                "SalesInvoices" => new SalesInvoicesPage(),
                "SalesReturns" => new SalesReturnsPage(),
                "PurchaseInvoices" => new PurchaseInvoicesPage(),
                "PurchaseReturns" => new PurchaseReturnsPage(),
                "Products" => new ProductsPage(),
                "Inventory" => new InventoryPage(),
                "Customers" => new CustomersPage(),
                "Suppliers" => new SuppliersPage(),
                "ChartOfAccounts" => new ChartOfAccountsPage(),
                "JournalEntries" => new JournalEntriesPage(),
                "FinancialReports" => new FinancialReportsPage(),
                "InventoryReports" => new InventoryReportsPage(),
                "Users" => new UsersPage(),
                "CompanySettings" => new CompanySettingsPage(),
                _ => new DashboardPage()
            };

            MainContentFrame.Navigate(page);
        }

        private void UpdatePageTitle(string pageTag)
        {
            PageTitleTextBlock.Text = GetPageDisplayName(pageTag);
        }

        private string GetPageDisplayName(string pageTag)
        {
            return pageTag switch
            {
                "Dashboard" => "لوحة التحكم",
                "SalesInvoices" => "فواتير المبيعات",
                "SalesReturns" => "مرتجع المبيعات",
                "PurchaseInvoices" => "فواتير المشتريات",
                "PurchaseReturns" => "مرتجع المشتريات",
                "Products" => "الأصناف",
                "Inventory" => "إدارة المخزون",
                "Customers" => "العملاء",
                "Suppliers" => "الموردين",
                "ChartOfAccounts" => "شجرة الحسابات",
                "JournalEntries" => "القيود اليومية",
                "FinancialReports" => "التقارير المالية",
                "InventoryReports" => "تقارير المخزون",
                "Users" => "المستخدمين",
                "CompanySettings" => "إعدادات الشركة",
                _ => "لوحة التحكم"
            };
        }

        private void LoadDashboard()
        {
            NavigateToPage("Dashboard");
            UpdatePageTitle("Dashboard");
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج من النظام؟", 
                "تسجيل الخروج", 
                MessageBoxButton.YesNo, 
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // Clear current user
                Application.Current.Properties["CurrentUser"] = null;

                // Stop timer
                _timer?.Stop();

                // Show login window
                var loginWindow = new LoginWindow();
                loginWindow.Show();

                // Close main window
                this.Close();
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }
    }
}