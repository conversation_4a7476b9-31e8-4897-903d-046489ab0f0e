using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace KiloAccounting.Models
{
    public class JournalEntry
    {
        [Key]
        public int JournalEntryId { get; set; }

        [Required]
        [StringLength(50)]
        public string EntryNumber { get; set; }

        public DateTime EntryDate { get; set; }

        [Required]
        [StringLength(200)]
        public string Description { get; set; }

        [StringLength(50)]
        public string ReferenceNumber { get; set; }

        public JournalEntryType EntryType { get; set; }

        public JournalEntryStatus Status { get; set; } = JournalEntryStatus.Draft;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalDebit { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCredit { get; set; } = 0;

        public bool IsBalanced => TotalDebit == TotalCredit;

        public bool IsPosted { get; set; } = false;

        public DateTime? PostedDate { get; set; }

        public int? PostedBy { get; set; }

        [StringLength(500)]
        public string Notes { get; set; }

        public int? SourceInvoiceId { get; set; }

        public int? SourcePurchaseInvoiceId { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        public int CreatedBy { get; set; }

        // Navigation properties
        public virtual ICollection<JournalEntryLine> JournalEntryLines { get; set; } = new List<JournalEntryLine>();
    }

    public class JournalEntryLine
    {
        [Key]
        public int JournalEntryLineId { get; set; }

        public int JournalEntryId { get; set; }

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry JournalEntry { get; set; }

        public int AccountId { get; set; }

        [ForeignKey("AccountId")]
        public virtual Account Account { get; set; }

        [StringLength(200)]
        public string Description { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DebitAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditAmount { get; set; } = 0;

        public int LineNumber { get; set; }

        [StringLength(50)]
        public string ReferenceNumber { get; set; }

        public int? CustomerId { get; set; }

        public int? SupplierId { get; set; }

        public int? ProductId { get; set; }
    }

    public enum JournalEntryType
    {
        Manual = 1,             // يدوي
        SalesInvoice = 2,       // فاتورة مبيعات
        PurchaseInvoice = 3,    // فاتورة مشتريات
        Payment = 4,            // دفعة
        Receipt = 5,            // إيصال
        Adjustment = 6,         // تسوية
        Opening = 7,            // رصيد افتتاحي
        Closing = 8,            // إقفال
        Transfer = 9            // تحويل
    }

    public enum JournalEntryStatus
    {
        Draft = 1,      // مسودة
        Pending = 2,    // معلقة
        Approved = 3,   // معتمدة
        Posted = 4,     // مرحلة
        Cancelled = 5   // ملغاة
    }

    public class Partner
    {
        [Key]
        public int PartnerId { get; set; }

        [Required]
        [StringLength(100)]
        public string PartnerName { get; set; }

        [StringLength(100)]
        public string PartnerNameEnglish { get; set; }

        [StringLength(20)]
        public string Phone { get; set; }

        [StringLength(100)]
        public string Email { get; set; }

        [StringLength(200)]
        public string Address { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal PartnershipPercentage { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal CapitalContribution { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentCapital { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal WithdrawalsToDate { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal ProfitShareToDate { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public DateTime JoinDate { get; set; }

        public DateTime? LeaveDate { get; set; }

        [StringLength(500)]
        public string Notes { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public int CreatedBy { get; set; }

        // Navigation properties
        public virtual ICollection<PartnerTransaction> Transactions { get; set; } = new List<PartnerTransaction>();
    }

    public class PartnerTransaction
    {
        [Key]
        public int TransactionId { get; set; }

        public int PartnerId { get; set; }

        [ForeignKey("PartnerId")]
        public virtual Partner Partner { get; set; }

        public DateTime TransactionDate { get; set; }

        public PartnerTransactionType TransactionType { get; set; }

        [Required]
        [StringLength(200)]
        public string Description { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [StringLength(50)]
        public string ReferenceNumber { get; set; }

        public int? JournalEntryId { get; set; }

        [ForeignKey("JournalEntryId")]
        public virtual JournalEntry JournalEntry { get; set; }

        public int CreatedBy { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    public enum PartnerTransactionType
    {
        CapitalContribution = 1,    // إيداع رأس مال
        Withdrawal = 2,             // سحب
        ProfitDistribution = 3,     // توزيع أرباح
        LossAllocation = 4,         // تحميل خسائر
        CapitalAdjustment = 5       // تعديل رأس المال
    }
}