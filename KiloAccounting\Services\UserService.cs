using KiloAccounting.Data;
using KiloAccounting.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using BCrypt.Net;

namespace KiloAccounting.Services
{
    public class UserService : IUserService
    {
        private readonly AccountingDbContext _context;

        public UserService(AccountingDbContext context)
        {
            _context = context;
        }

        public async Task<User> AuthenticateAsync(string username, string password)
        {
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                return null;

            var user = await _context.Users
                .FirstOrDefaultAsync(u => u.Username == username && u.IsActive);

            if (user == null)
                return null;

            if (!BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
                return null;

            return user;
        }

        public async Task<User> GetUserByIdAsync(int userId)
        {
            return await _context.Users
                .FirstOrDefaultAsync(u => u.UserId == userId);
        }

        public async Task<User> GetUserByUsernameAsync(string username)
        {
            return await _context.Users
                .FirstOrDefaultAsync(u => u.Username == username);
        }

        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            return await _context.Users
                .OrderBy(u => u.FullName)
                .ToListAsync();
        }

        public async Task<User> CreateUserAsync(User user, string password)
        {
            if (await IsUsernameAvailableAsync(user.Username) == false)
                throw new InvalidOperationException("اسم المستخدم موجود بالفعل");

            if (await IsEmailAvailableAsync(user.Email) == false)
                throw new InvalidOperationException("البريد الإلكتروني موجود بالفعل");

            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(password);
            user.CreatedDate = DateTime.Now;

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            return user;
        }

        public async Task<User> UpdateUserAsync(User user)
        {
            var existingUser = await GetUserByIdAsync(user.UserId);
            if (existingUser == null)
                throw new InvalidOperationException("المستخدم غير موجود");

            // Check if username is available (excluding current user)
            var usernameExists = await _context.Users
                .AnyAsync(u => u.Username == user.Username && u.UserId != user.UserId);
            if (usernameExists)
                throw new InvalidOperationException("اسم المستخدم موجود بالفعل");

            // Check if email is available (excluding current user)
            var emailExists = await _context.Users
                .AnyAsync(u => u.Email == user.Email && u.UserId != user.UserId);
            if (emailExists)
                throw new InvalidOperationException("البريد الإلكتروني موجود بالفعل");

            existingUser.Username = user.Username;
            existingUser.FullName = user.FullName;
            existingUser.Email = user.Email;
            existingUser.Role = user.Role;
            existingUser.IsActive = user.IsActive;
            existingUser.PhoneNumber = user.PhoneNumber;
            existingUser.Address = user.Address;
            existingUser.Salary = user.Salary;
            existingUser.HireDate = user.HireDate;

            await _context.SaveChangesAsync();
            return existingUser;
        }

        public async Task<bool> DeleteUserAsync(int userId)
        {
            var user = await GetUserByIdAsync(userId);
            if (user == null)
                return false;

            // Don't allow deletion of the last admin user
            if (user.Role == UserRole.Manager)
            {
                var adminCount = await _context.Users
                    .CountAsync(u => u.Role == UserRole.Manager && u.IsActive);
                if (adminCount <= 1)
                    throw new InvalidOperationException("لا يمكن حذف آخر مدير في النظام");
            }

            // Soft delete - just deactivate the user
            user.IsActive = false;
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            var user = await GetUserByIdAsync(userId);
            if (user == null)
                return false;

            if (!BCrypt.Net.BCrypt.Verify(currentPassword, user.PasswordHash))
                return false;

            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<bool> ResetPasswordAsync(int userId, string newPassword)
        {
            var user = await GetUserByIdAsync(userId);
            if (user == null)
                return false;

            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<bool> IsUsernameAvailableAsync(string username)
        {
            return !await _context.Users
                .AnyAsync(u => u.Username == username);
        }

        public async Task<bool> IsEmailAvailableAsync(string email)
        {
            return !await _context.Users
                .AnyAsync(u => u.Email == email);
        }

        public async Task UpdateLastLoginAsync(int userId)
        {
            var user = await GetUserByIdAsync(userId);
            if (user != null)
            {
                user.LastLoginDate = DateTime.Now;
                await _context.SaveChangesAsync();
            }
        }
    }
}