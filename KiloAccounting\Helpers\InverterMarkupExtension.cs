using System;
using System.Windows;
using System.Windows.Markup;

namespace KiloAccounting.Helpers
{
    /// <summary>
    /// Custom markup extension to replace the problematic inverterMarkupExtension
    /// </summary>
    public class InverterMarkupExtension : MarkupExtension
    {
        public InverterMarkupExtension() { }

        public InverterMarkupExtension(object parameter)
        {
            Parameter = parameter;
        }

        [ConstructorArgument("parameter")]
        public object Parameter { get; set; }

        public override object ProvideValue(IServiceProvider serviceProvider)
        {
            try
            {
                // If the parameter is a boolean, invert it
                if (Parameter is bool boolValue)
                {
                    return !boolValue;
                }

                // For other types, return the parameter as is
                return Parameter;
            }
            catch (Exception ex)
            {
                // Log the error
                Console.WriteLine($"Error in InverterMarkupExtension: {ex.Message}");
                
                // Return a default value
                return DependencyProperty.UnsetValue;
            }
        }
    }
}