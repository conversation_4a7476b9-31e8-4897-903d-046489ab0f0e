<Page x:Class="KiloAccounting.Views.Pages.SalesInvoicesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="فواتير المبيعات"
      FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Page Title -->
        <TextBlock Grid.Row="0" 
                   Text="فواتير المبيعات" 
                   Style="{StaticResource PageTitle}"/>

        <!-- Toolbar -->
        <Border Grid.Row="1" Style="{StaticResource ModernCard}">
            <StackPanel Orientation="Horizontal">
                <Button Style="{StaticResource ActionButton}"
                        Click="NewInvoice_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Plus" Margin="0,0,5,0"/>
                        <TextBlock Text="فاتورة جديدة"/>
                    </StackPanel>
                </Button>
                
                <Button Style="{StaticResource SecondaryButton}"
                        Click="Refresh_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh" Margin="0,0,5,0"/>
                        <TextBlock Text="تحديث"/>
                    </StackPanel>
                </Button>

                <Separator Margin="10,0"/>

                <TextBox x:Name="SearchTextBox"
                         Style="{StaticResource SearchTextBox}"
                         Width="250"
                         TextChanged="Search_TextChanged"/>
            </StackPanel>
        </Border>

        <!-- Data Grid -->
        <Border Grid.Row="2" Style="{StaticResource ModernCard}">
            <DataGrid x:Name="InvoicesDataGrid" 
                      Style="{StaticResource ModernDataGrid}"
                      MouseDoubleClick="InvoicesDataGrid_MouseDoubleClick">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                    <DataGridTextColumn Header="التاريخ" Binding="{Binding InvoiceDate, StringFormat=yyyy/MM/dd}" Width="100"/>
                    <DataGridTextColumn Header="العميل" Binding="{Binding Customer.CustomerName}" Width="200"/>
                    <DataGridTextColumn Header="المبلغ الإجمالي" Binding="{Binding TotalAmount, StringFormat=N2}" Width="120"/>
                    <DataGridTextColumn Header="المبلغ المدفوع" Binding="{Binding PaidAmount, StringFormat=N2}" Width="120"/>
                    <DataGridTextColumn Header="المبلغ المتبقي" Binding="{Binding RemainingAmount, StringFormat=N2}" Width="120"/>
                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Style="{StaticResource IconButton}"
                                            ToolTip="تعديل"
                                            Click="Edit_Click"
                                            Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="Edit"/>
                                    </Button>
                                    <Button Style="{StaticResource IconButton}"
                                            ToolTip="طباعة"
                                            Click="Print_Click"
                                            Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="Printer"/>
                                    </Button>
                                    <Button Style="{StaticResource IconButton}"
                                            ToolTip="حذف"
                                            Click="Delete_Click"
                                            Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="Delete"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>
    </Grid>
</Page>