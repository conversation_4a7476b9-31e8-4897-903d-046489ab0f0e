using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace KiloAccounting.Models
{
    public class Product
    {
        [Key]
        public int ProductId { get; set; }

        [Required]
        [StringLength(50)]
        public string ProductCode { get; set; }

        [Required]
        [StringLength(100)]
        public string ProductName { get; set; }

        [StringLength(100)]
        public string ProductNameEnglish { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [StringLength(50)]
        public string Barcode { get; set; }

        public int CategoryId { get; set; }

        [ForeignKey("CategoryId")]
        public virtual ProductCategory Category { get; set; }

        public int UnitId { get; set; }

        [ForeignKey("UnitId")]
        public virtual Unit Unit { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchasePrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal SalePrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal MinimumStock { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal MaximumStock { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal ReorderLevel { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentStock { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal ReservedStock { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal AvailableStock { get; set; } = 0;

        public bool TrackInventory { get; set; } = true;

        public bool IsActive { get; set; } = true;

        public bool AllowNegativeStock { get; set; } = false;

        [Column(TypeName = "decimal(5,2)")]
        public decimal VATRate { get; set; } = 0.15m;

        public bool IsVATExempt { get; set; } = false;

        public byte[] Image { get; set; }

        [StringLength(500)]
        public string Notes { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        public int CreatedBy { get; set; }

        // Navigation properties
        public virtual ICollection<InvoiceItem> InvoiceItems { get; set; } = new List<InvoiceItem>();
        public virtual ICollection<PurchaseInvoiceItem> PurchaseInvoiceItems { get; set; } = new List<PurchaseInvoiceItem>();
        public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
        public virtual ICollection<StockMovement> StockMovements { get; set; } = new List<StockMovement>();
    }

    public class ProductCategory
    {
        [Key]
        public int CategoryId { get; set; }

        [Required]
        [StringLength(50)]
        public string CategoryCode { get; set; }

        [Required]
        [StringLength(100)]
        public string CategoryName { get; set; }

        [StringLength(100)]
        public string CategoryNameEnglish { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        public int? ParentCategoryId { get; set; }

        [ForeignKey("ParentCategoryId")]
        public virtual ProductCategory ParentCategory { get; set; }

        public virtual ICollection<ProductCategory> SubCategories { get; set; } = new List<ProductCategory>();

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }

    public class Unit
    {
        [Key]
        public int UnitId { get; set; }

        [Required]
        [StringLength(20)]
        public string UnitCode { get; set; }

        [Required]
        [StringLength(50)]
        public string UnitName { get; set; }

        [StringLength(50)]
        public string UnitNameEnglish { get; set; }

        [StringLength(10)]
        public string Symbol { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }

    public class InventoryTransaction
    {
        [Key]
        public int TransactionId { get; set; }

        public int ProductId { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        public DateTime TransactionDate { get; set; }

        public InventoryTransactionType TransactionType { get; set; }

        [StringLength(50)]
        public string ReferenceNumber { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Quantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; }

        [StringLength(200)]
        public string Description { get; set; }

        public int? InvoiceId { get; set; }

        public int? PurchaseInvoiceId { get; set; }

        public int CreatedBy { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    public enum InventoryTransactionType
    {
        Purchase = 1,           // شراء
        Sale = 2,               // بيع
        Adjustment = 3,         // تسوية
        Transfer = 4,           // تحويل
        Return = 5,             // مرتجع
        Opening = 6,            // رصيد افتتاحي
        Damage = 7,             // تالف
        Loss = 8                // فقدان
    }

    public class StockMovement
    {
        [Key]
        public int MovementId { get; set; }

        public int ProductId { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        public DateTime MovementDate { get; set; }

        public StockMovementType MovementType { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal QuantityIn { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal QuantityOut { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Balance { get; set; } = 0;

        [StringLength(50)]
        public string ReferenceNumber { get; set; }

        [StringLength(200)]
        public string Description { get; set; }

        public int CreatedBy { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    public enum StockMovementType
    {
        In = 1,     // وارد
        Out = 2     // صادر
    }
}