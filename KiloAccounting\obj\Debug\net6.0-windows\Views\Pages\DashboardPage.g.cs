﻿#pragma checksum "..\..\..\..\..\Views\Pages\DashboardPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4725F320C1AAC5D13612AD4F65A9EFD1B87EA4C4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace KiloAccounting.Views.Pages {
    
    
    /// <summary>
    /// DashboardPage
    /// </summary>
    public partial class DashboardPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 56 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WelcomeMessageTextBlock;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastLoginTextBlock;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodaySalesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodayPurchasesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalCustomersTextBlock;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LowStockItemsTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/KiloAccounting;V1.0.0.0;component/views/pages/dashboardpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.4.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WelcomeMessageTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.LastLoginTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TodaySalesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TodayPurchasesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TotalCustomersTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.LowStockItemsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            
            #line 249 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAction_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 259 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAction_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 269 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAction_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 279 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAction_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 289 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAction_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 299 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAction_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 309 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAction_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 319 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickAction_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

