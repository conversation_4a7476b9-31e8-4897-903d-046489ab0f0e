using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace KiloAccounting.Models
{
    public class Account
    {
        [Key]
        public int AccountId { get; set; }

        [Required]
        [StringLength(20)]
        public string AccountCode { get; set; }

        [Required]
        [StringLength(100)]
        public string AccountName { get; set; }

        [StringLength(100)]
        public string AccountNameEnglish { get; set; }

        [Required]
        public AccountType AccountType { get; set; }

        [Required]
        public AccountCategory Category { get; set; }

        public int? ParentAccountId { get; set; }

        [ForeignKey("ParentAccountId")]
        public virtual Account ParentAccount { get; set; }

        public virtual ICollection<Account> SubAccounts { get; set; } = new List<Account>();

        public bool IsActive { get; set; } = true;

        public bool AllowPosting { get; set; } = true;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        public BalanceType BalanceType { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        public int CreatedBy { get; set; }

        // Navigation properties
        public virtual ICollection<JournalEntryLine> JournalEntryLines { get; set; } = new List<JournalEntryLine>();
        public virtual ICollection<Customer> Customers { get; set; } = new List<Customer>();
        public virtual ICollection<Supplier> Suppliers { get; set; } = new List<Supplier>();
    }

    public enum AccountType
    {
        Asset = 1,          // أصول
        Liability = 2,      // خصوم
        Equity = 3,         // حقوق الملكية
        Revenue = 4,        // إيرادات
        Expense = 5,        // مصروفات
        Cost = 6           // تكلفة البضاعة المباعة
    }

    public enum AccountCategory
    {
        // Assets
        CurrentAssets = 101,        // أصول متداولة
        FixedAssets = 102,          // أصول ثابتة
        IntangibleAssets = 103,     // أصول غير ملموسة
        
        // Liabilities
        CurrentLiabilities = 201,   // خصوم متداولة
        LongTermLiabilities = 202,  // خصوم طويلة الأجل
        
        // Equity
        Capital = 301,              // رأس المال
        RetainedEarnings = 302,     // الأرباح المحتجزة
        
        // Revenue
        SalesRevenue = 401,         // إيرادات المبيعات
        OtherRevenue = 402,         // إيرادات أخرى
        
        // Expenses
        OperatingExpenses = 501,    // مصروفات تشغيلية
        AdministrativeExpenses = 502, // مصروفات إدارية
        SellingExpenses = 503,      // مصروفات بيع
        FinancialExpenses = 504,    // مصروفات مالية
        
        // Cost
        CostOfGoodsSold = 601      // تكلفة البضاعة المباعة
    }

    public enum BalanceType
    {
        Debit = 1,     // مدين
        Credit = 2     // دائن
    }
}