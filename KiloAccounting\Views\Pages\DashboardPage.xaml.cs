using System;
using System.Windows;
using System.Windows.Controls;
using KiloAccounting.Models;

namespace KiloAccounting.Views.Pages
{
    public partial class DashboardPage : Page
    {
        private User _currentUser;

        public DashboardPage()
        {
            InitializeComponent();
            LoadDashboardData();
        }

        private void LoadDashboardData()
        {
            _currentUser = Application.Current.Properties["CurrentUser"] as User;
            
            if (_currentUser != null)
            {
                WelcomeMessageTextBlock.Text = $"أهلاً وسهلاً بك، {_currentUser.FullName}";
                
                if (_currentUser.LastLoginDate.HasValue)
                {
                    LastLoginTextBlock.Text = $"آخر تسجيل دخول: {_currentUser.LastLoginDate.Value:yyyy/MM/dd HH:mm}";
                }
                else
                {
                    LastLoginTextBlock.Text = "هذا هو تسجيل الدخول الأول";
                }
            }

            // Load statistics (these would normally come from the database)
            LoadStatistics();
        }

        private async void LoadStatistics()
        {
            try
            {
                // In a real application, these would be loaded from the database
                // For now, we'll use sample data
                
                // Today's sales
                TodaySalesTextBlock.Text = "15,750";
                
                // Today's purchases
                TodayPurchasesTextBlock.Text = "8,500";
                
                // Total customers
                TotalCustomersTextBlock.Text = "127";
                
                // Low stock items
                LowStockItemsTextBlock.Text = "5";

                // TODO: Load real data from services
                // var invoiceService = App.GetService<IInvoiceService>();
                // var customerService = App.GetService<ICustomerService>();
                // var inventoryService = App.GetService<IInventoryService>();
                
                // var todaySales = await invoiceService.GetTodaySalesAsync();
                // var todayPurchases = await invoiceService.GetTodayPurchasesAsync();
                // var totalCustomers = await customerService.GetCustomerCountAsync();
                // var lowStockItems = await inventoryService.GetLowStockCountAsync();
                
                // TodaySalesTextBlock.Text = todaySales.ToString("N0");
                // TodayPurchasesTextBlock.Text = todayPurchases.ToString("N0");
                // TotalCustomersTextBlock.Text = totalCustomers.ToString();
                // LowStockItemsTextBlock.Text = lowStockItems.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}", 
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void QuickAction_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string action)
            {
                HandleQuickAction(action);
            }
        }

        private void HandleQuickAction(string action)
        {
            try
            {
                switch (action)
                {
                    case "NewSalesInvoice":
                        MessageBox.Show("سيتم فتح نافذة فاتورة مبيعات جديدة", "إجراء سريع", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        // TODO: Navigate to new sales invoice
                        break;

                    case "NewPurchaseInvoice":
                        MessageBox.Show("سيتم فتح نافذة فاتورة مشتريات جديدة", "إجراء سريع", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        // TODO: Navigate to new purchase invoice
                        break;

                    case "NewCustomer":
                        MessageBox.Show("سيتم فتح نافذة إضافة عميل جديد", "إجراء سريع", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        // TODO: Navigate to new customer
                        break;

                    case "NewProduct":
                        MessageBox.Show("سيتم فتح نافذة إضافة صنف جديد", "إجراء سريع", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        // TODO: Navigate to new product
                        break;

                    case "InventoryReport":
                        MessageBox.Show("سيتم فتح تقرير المخزون", "إجراء سريع", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        // TODO: Navigate to inventory report
                        break;

                    case "FinancialReport":
                        MessageBox.Show("سيتم فتح التقارير المالية", "إجراء سريع", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        // TODO: Navigate to financial reports
                        break;

                    case "Backup":
                        MessageBox.Show("سيتم بدء عملية النسخ الاحتياطي", "إجراء سريع", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        // TODO: Start backup process
                        break;

                    case "Settings":
                        MessageBox.Show("سيتم فتح إعدادات النظام", "إجراء سريع", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        // TODO: Navigate to settings
                        break;

                    default:
                        MessageBox.Show("إجراء غير معروف", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تنفيذ الإجراء: {ex.Message}", 
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}