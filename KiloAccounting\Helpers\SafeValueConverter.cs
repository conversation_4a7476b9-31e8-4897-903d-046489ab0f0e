using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace KiloAccounting.Helpers
{
    /// <summary>
    /// A safe value converter that catches and handles exceptions
    /// </summary>
    public class SafeValueConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                // Handle boolean inversion (which is what inverterMarkupExtension often does)
                if (value is bool boolValue && targetType == typeof(bool))
                {
                    return !boolValue;
                }

                // Handle visibility conversion
                if (value is bool visibilityBool && targetType == typeof(Visibility))
                {
                    return visibilityBool ? Visibility.Visible : Visibility.Collapsed;
                }

                // For other types, return the value as is
                return value;
            }
            catch (Exception ex)
            {
                // Log the error
                Console.WriteLine($"Error in SafeValueConverter: {ex.Message}");
                
                // Return a default value based on the target type
                if (targetType == typeof(bool))
                    return false;
                if (targetType == typeof(Visibility))
                    return Visibility.Collapsed;
                
                return DependencyProperty.UnsetValue;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                // Handle boolean inversion
                if (value is bool boolValue && targetType == typeof(bool))
                {
                    return !boolValue;
                }

                // Handle visibility conversion
                if (value is Visibility visibility && targetType == typeof(bool))
                {
                    return visibility == Visibility.Visible;
                }

                // For other types, return the value as is
                return value;
            }
            catch (Exception ex)
            {
                // Log the error
                Console.WriteLine($"Error in SafeValueConverter.ConvertBack: {ex.Message}");
                
                // Return a default value
                return DependencyProperty.UnsetValue;
            }
        }
    }
}