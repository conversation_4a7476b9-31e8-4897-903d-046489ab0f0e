using System;
using System.Windows;
using System.Windows.Input;
using KiloAccounting.Services;
using KiloAccounting.Models;

namespace KiloAccounting.Views
{
    public partial class LoginWindow : Window
    {
        private readonly IUserService _userService;

        public LoginWindow()
        {
            InitializeComponent();
            _userService = App.GetService<IUserService>();
            
            // Set focus to username textbox
            Loaded += (s, e) => UsernameTextBox.Focus();
            
            // Handle Enter key in password box
            PasswordBox.KeyDown += (s, e) =>
            {
                if (e.Key == Key.Enter)
                    LoginButton_Click(s, e);
            };
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoginButton.IsEnabled = false;
                ErrorMessageTextBlock.Visibility = Visibility.Collapsed;

                var username = UsernameTextBox.Text.Trim();
                var password = PasswordBox.Password;

                if (string.IsNullOrEmpty(username))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    UsernameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrEmpty(password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    PasswordBox.Focus();
                    return;
                }

                var user = await _userService.AuthenticateAsync(username, password);

                if (user == null)
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                    PasswordBox.Clear();
                    PasswordBox.Focus();
                    return;
                }

                if (!user.IsActive)
                {
                    ShowError("حساب المستخدم غير مفعل. يرجى الاتصال بالمدير");
                    return;
                }

                // Update last login
                await _userService.UpdateLastLoginAsync(user.UserId);

                // Store current user in application
                Application.Current.Properties["CurrentUser"] = user;

                // Save remember me preference
                if (RememberMeCheckBox.IsChecked == true)
                {
                    Properties.Settings.Default.RememberUsername = username;
                    Properties.Settings.Default.Save();
                }
                else
                {
                    Properties.Settings.Default.RememberUsername = string.Empty;
                    Properties.Settings.Default.Save();
                }

                try
                {
                    // Open main window with error handling
                    var mainWindow = new MainWindow();
                    mainWindow.Show();

                    // Close login window
                    this.Close();
                }
                catch (System.Windows.Markup.XamlParseException xamlEx)
                {
                    if (xamlEx.Message.Contains("inverterMarkupExtension") ||
                        xamlEx.Message.Contains("MarkupExtension"))
                    {
                        MessageBox.Show("حدث خطأ في تحميل واجهة المستخدم. سيتم محاولة إعادة تشغيل التطبيق.",
                            "خطأ في الواجهة", MessageBoxButton.OK, MessageBoxImage.Warning);
                        
                        // Try to restart the application
                        System.Diagnostics.Process.Start(Environment.ProcessPath ?? "KiloAccounting.exe");
                        Application.Current.Shutdown();
                    }
                    else
                    {
                        throw; // Re-throw if it's not the specific error we're handling
                    }
                }
            }
            catch (Exception ex)
            {
                ShowError($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                LoginButton.IsEnabled = true;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void ResetPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("يرجى الاتصال بمدير النظام لإعادة تعيين كلمة المرور", 
                "إعادة تعيين كلمة المرور", 
                MessageBoxButton.OK, 
                MessageBoxImage.Information);
        }

        private void ShowError(string message)
        {
            ErrorMessageTextBlock.Text = message;
            ErrorMessageTextBlock.Visibility = Visibility.Visible;
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // Load remembered username
            var rememberedUsername = Properties.Settings.Default.RememberUsername;
            if (!string.IsNullOrEmpty(rememberedUsername))
            {
                UsernameTextBox.Text = rememberedUsername;
                RememberMeCheckBox.IsChecked = true;
                PasswordBox.Focus();
            }
        }

        // Allow window dragging
        private void Window_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ButtonState == MouseButtonState.Pressed)
                this.DragMove();
        }
    }
}