using System;
using System.Globalization;
using System.Threading;
using System.Windows;
using System.Windows.Markup;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using KiloAccounting.Data;
using KiloAccounting.Services;

namespace KiloAccounting
{
    public partial class App : Application
    {
        private ServiceProvider _serviceProvider;
        private IConfiguration _configuration;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Apply MaterialDesign fix
            MaterialDesignFix.ApplyFix();

            // Configure services
            ConfigureServices();

            // Initialize database
            InitializeDatabase();

            // Set default culture (Arabic)
            SetCulture("ar-SA");

            // Handle unhandled exceptions
            DispatcherUnhandledException += App_DispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            
            // Handle XAML parsing errors
            EventManager.RegisterClassHandler(typeof(FrameworkElement), FrameworkElement.LoadedEvent,
                new RoutedEventHandler(OnFrameworkElementLoaded));
        }

        private void ConfigureServices()
        {
            var services = new ServiceCollection();

            // Configuration
            var builder = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
            
            _configuration = builder.Build();
            services.AddSingleton(_configuration);

            // Logging
            services.AddLogging();

            // Database
            services.AddDbContext<AccountingDbContext>();
            
            // Services - Only add implemented services
            services.AddScoped<IUserService, UserService>();
            
            // TODO: Add other services when implemented
            // services.AddScoped<ICompanyService, CompanyService>();
            // services.AddScoped<IAccountService, AccountService>();
            // services.AddScoped<IInvoiceService, InvoiceService>();
            // services.AddScoped<IInventoryService, InventoryService>();
            // services.AddScoped<ICustomerService, CustomerService>();
            // services.AddScoped<ISupplierService, SupplierService>();
            // services.AddScoped<IReportService, ReportService>();
            // services.AddScoped<IBackupService, BackupService>();
            // services.AddScoped<ILocalizationService, LocalizationService>();
            // services.AddScoped<IExportService, ExportService>();

            // ViewModels - TODO: Add when implemented
            // services.AddTransient<LoginViewModel>();
            // services.AddTransient<MainViewModel>();
            // services.AddTransient<DashboardViewModel>();

            _serviceProvider = services.BuildServiceProvider();
        }

        private void InitializeDatabase()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AccountingDbContext>();
                context.Database.EnsureCreated();
                
                // Seed initial data
                DatabaseSeeder.SeedData(context);
            }
            catch (Exception ex)
            {
                var logger = _serviceProvider.GetService<ILogger<App>>();
                logger?.LogError(ex, "An error occurred while initializing the database.");
                
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", 
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SetCulture(string culture)
        {
            var cultureInfo = new CultureInfo(culture);
            Thread.CurrentThread.CurrentCulture = cultureInfo;
            Thread.CurrentThread.CurrentUICulture = cultureInfo;
            CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
            CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;
        }

        public static T GetService<T>() where T : class
        {
            return ((App)Current)._serviceProvider.GetService<T>();
        }

        private void OnFrameworkElementLoaded(object sender, RoutedEventArgs e)
        {
            // This helps catch XAML loading issues early
        }

        private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            var logger = _serviceProvider.GetService<ILogger<App>>();
            logger?.LogError(e.Exception, "Unhandled exception occurred.");
            
            // Check if it's a markup extension error
            if (e.Exception is XamlParseException xamlEx)
            {
                logger?.LogError(xamlEx, "XAML Parse Exception: {Message}", xamlEx.Message);
                
                if (xamlEx.Message.Contains("inverterMarkupExtension") ||
                    xamlEx.Message.Contains("MarkupExtension"))
                {
                    MessageBox.Show("حدث خطأ في تحميل واجهة المستخدم. سيتم إعادة تشغيل التطبيق.",
                        "خطأ في الواجهة", MessageBoxButton.OK, MessageBoxImage.Warning);
                    
                    // Try to restart the application
                    System.Diagnostics.Process.Start(Environment.ProcessPath ?? "KiloAccounting.exe");
                    Application.Current.Shutdown();
                    return;
                }
            }
            
            MessageBox.Show($"حدث خطأ غير متوقع: {e.Exception.Message}",
                "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            
            e.Handled = true;
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var logger = _serviceProvider.GetService<ILogger<App>>();
            logger?.LogError(e.ExceptionObject as Exception, "Unhandled domain exception occurred.");
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _serviceProvider?.Dispose();
            base.OnExit(e);
        }
    }
}