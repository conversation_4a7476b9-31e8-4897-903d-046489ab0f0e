using System;
using System.Windows;
using System.Windows.Markup;
using System.Reflection;
using System.IO;
using System.Linq;

namespace KiloAccounting
{
    public static class MaterialDesignFix
    {
        public static void ApplyFix()
        {
            try
            {
                // Register our custom markup extensions and converters first
                RegisterCustomComponents();

                // Then initialize our MaterialDesignFixHelper
                KiloAccounting.Helpers.MaterialDesignFixHelper.Initialize();

                // Apply additional fixes
                ApplyAdditionalFixes();

                Console.WriteLine("MaterialDesign fix applied successfully");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error applying fix: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Safely adds or replaces a resource in the application resources
        /// </summary>
        /// <param name="key">The resource key</param>
        /// <param name="value">The resource value</param>
        private static void SafeAddOrReplaceResource(string key, object value)
        {
            try
            {
                if (Application.Current?.Resources != null)
                {
                    if (Application.Current.Resources.Contains(key))
                    {
                        Application.Current.Resources[key] = value;
                        Console.WriteLine($"TestFix: Replaced resource: {key}");
                    }
                    else
                    {
                        Application.Current.Resources.Add(key, value);
                        Console.WriteLine($"TestFix: Added resource: {key}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TestFix: Error adding/replacing resource {key}: {ex.Message}");
            }
        }

        private static void RegisterCustomComponents()
        {
            // Register our custom markup extensions and converters in the application resources
            if (Application.Current != null && Application.Current.Resources != null)
            {
                // Add our custom inverter markup extension
                var customInverter = new KiloAccounting.Helpers.InverterMarkupExtension();

                // Safely add/replace all inverter-related keys
                SafeAddOrReplaceResource("MaterialDesign.Inverter", customInverter);
                SafeAddOrReplaceResource("Inverter", customInverter);
                SafeAddOrReplaceResource("inverterMarkupExtension", customInverter);

                // Add our custom safe converter
                var safeConverter = new KiloAccounting.Helpers.SafeValueConverter();
                SafeAddOrReplaceResource("SafeConverter", safeConverter);
            }
        }

        private static void ApplyAdditionalFixes()
        {
            // Apply any additional fixes that might be needed

            // Fix for MaterialDesignThemes 4.8.0 - disable animations if they're causing issues
            try
            {
                // Find the MaterialDesignThemes assembly
                var materialDesignAssembly = AppDomain.CurrentDomain.GetAssemblies()
                    .FirstOrDefault(a => a.FullName.Contains("MaterialDesignThemes"));

                if (materialDesignAssembly != null)
                {
                    // Try to find the AnimationAssist type
                    var animationAssistType = materialDesignAssembly.GetTypes()
                        .FirstOrDefault(t => t.Name == "AnimationAssist");

                    if (animationAssistType != null)
                    {
                        // Try to find the DisableAnimationsProperty field
                        var disableAnimationsField = animationAssistType.GetField("DisableAnimationsProperty",
                            BindingFlags.Public | BindingFlags.Static);

                        if (disableAnimationsField != null)
                        {
                            // Get the DependencyProperty
                            var disableAnimationsProperty = disableAnimationsField.GetValue(null) as DependencyProperty;

                            if (disableAnimationsProperty != null)
                            {
                                // Set the DisableAnimations property to true for the application
                                Application.Current.Resources["MaterialDesignDisableAnimations"] = true;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error applying additional fixes: {ex.Message}");
            }
        }
    }
}