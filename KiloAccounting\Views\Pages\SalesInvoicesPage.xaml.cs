using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace KiloAccounting.Views.Pages
{
    public partial class SalesInvoicesPage : Page
    {
        public SalesInvoicesPage()
        {
            InitializeComponent();
            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // TODO: Load sales invoices from service
                // var invoiceService = App.GetService<IInvoiceService>();
                // var invoices = await invoiceService.GetAllInvoicesAsync();
                // InvoicesDataGrid.ItemsSource = invoices;
                
                // For now, show placeholder message
                MessageBox.Show("سيتم تحميل فواتير المبيعات من قاعدة البيانات", 
                    "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", 
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void NewInvoice_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة فاتورة مبيعات جديدة", 
                "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Refresh_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }

        private void Search_TextChanged(object sender, TextChangedEventArgs e)
        {
            // TODO: Implement search functionality
        }

        private void InvoicesDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (InvoicesDataGrid.SelectedItem != null)
            {
                MessageBox.Show("سيتم فتح تفاصيل الفاتورة", 
                    "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void Edit_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة تعديل الفاتورة", 
                "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Print_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم طباعة الفاتورة", 
                "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Delete_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد حذف هذه الفاتورة؟", 
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("تم حذف الفاتورة", 
                    "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
}