{"format": 1, "restore": {"D:\\REACT JS\\accounting with kilo\\KiloAccounting\\KiloAccounting.csproj": {}}, "projects": {"D:\\REACT JS\\accounting with kilo\\KiloAccounting\\KiloAccounting.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\REACT JS\\accounting with kilo\\KiloAccounting\\KiloAccounting.csproj", "projectName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectPath": "D:\\REACT JS\\accounting with kilo\\KiloAccounting\\KiloAccounting.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\REACT JS\\accounting with kilo\\KiloAccounting\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "EPPlus": {"target": "Package", "version": "[7.0.4, )"}, "MaterialDesignColors": {"target": "Package", "version": "[2.1.2, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.8.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[7.0.14, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.118, )"}, "iTextSharp": {"target": "Package", "version": "[********, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}