<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Ultra Modern Card Style -->
    <Style x:Key="ModernCard" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource CardGradientBrush}"/>
        <Setter Property="CornerRadius" Value="16"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#667eea" BlurRadius="20" ShadowDepth="0" Opacity="0.1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#667eea" BlurRadius="25" ShadowDepth="0" Opacity="0.2"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Glassmorphism Card Style -->
    <Style x:Key="GlassCard" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <SolidColorBrush Color="White" Opacity="0.8"/>
            </Setter.Value>
        </Setter>
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="Effect">
            <Setter.Value>
                <BlurEffect Radius="10"/>
            </Setter.Value>
        </Setter>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Padding" Value="25"/>
        <Setter Property="BorderBrush">
            <Setter.Value>
                <SolidColorBrush Color="White" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- Ultra Modern Data Grid Style -->
    <Style x:Key="ModernDataGrid" TargetType="DataGrid">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="GridLinesVisibility" Value="None"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="AlternatingRowBackground" Value="{StaticResource LightGrayBrush}"/>
        <Setter Property="RowBackground" Value="White"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,10"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="RowHeight" Value="48"/>
        <Setter Property="ColumnHeaderHeight" Value="56"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGrid">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="12"
                            Padding="0">
                        <ScrollViewer x:Name="DG_ScrollViewer" Focusable="false">
                            <ScrollViewer.Template>
                                <ControlTemplate TargetType="ScrollViewer">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="*"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <DataGridColumnHeadersPresenter x:Name="PART_ColumnHeadersPresenter"
                                                                       Grid.Column="1"
                                                                       Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.Column}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"/>

                                        <ScrollContentPresenter x:Name="PART_ScrollContentPresenter"
                                                              Grid.Row="1" Grid.ColumnSpan="2"
                                                              CanContentScroll="{TemplateBinding CanContentScroll}"/>

                                        <ScrollBar x:Name="PART_VerticalScrollBar"
                                                 Grid.Column="2" Grid.Row="1"
                                                 Orientation="Vertical"
                                                 ViewportSize="{TemplateBinding ViewportHeight}"
                                                 Maximum="{TemplateBinding ScrollableHeight}"
                                                 Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                                 Value="{Binding VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>

                                        <ScrollBar x:Name="PART_HorizontalScrollBar"
                                                 Grid.Row="2" Grid.ColumnSpan="2"
                                                 Orientation="Horizontal"
                                                 ViewportSize="{TemplateBinding ViewportWidth}"
                                                 Maximum="{TemplateBinding ScrollableWidth}"
                                                 Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                                 Value="{Binding HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"/>
                                    </Grid>
                                </ControlTemplate>
                            </ScrollViewer.Template>
                            <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Resources>
            <!-- Modern Column Header Style -->
            <Style TargetType="DataGridColumnHeader">
                <Setter Property="Background" Value="{StaticResource PrimaryGradientBrush}"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Padding" Value="16,12"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="HorizontalContentAlignment" Value="Center"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
            </Style>

            <!-- Modern Row Style -->
            <Style TargetType="DataGridRow">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Margin" Value="0,2"/>
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Background" Value="{StaticResource SecondaryGradientBrush}"/>
                    </Trigger>
                    <Trigger Property="IsSelected" Value="True">
                        <Setter Property="Background" Value="{StaticResource PrimaryGradientBrush}"/>
                        <Setter Property="Foreground" Value="White"/>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!-- Modern Cell Style -->
            <Style TargetType="DataGridCell">
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="DataGridCell">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
                <Style.Triggers>
                    <Trigger Property="IsSelected" Value="True">
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="Foreground" Value="{Binding Foreground, RelativeSource={RelativeSource AncestorType=DataGridRow}}"/>
                    </Trigger>
                </Style.Triggers>
            </Style>
        </Style.Resources>
    </Style>

    <!-- Form Section Style -->
    <Style x:Key="FormSection" TargetType="GroupBox" BasedOn="{StaticResource MaterialDesignGroupBox}">
        <Setter Property="Margin" Value="0,10"/>
        <Setter Property="Padding" Value="15"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- Form Grid Style -->
    <Style x:Key="FormGrid" TargetType="Grid">
        <Setter Property="Margin" Value="10"/>
    </Style>

    <!-- Ultra Modern Navigation Button Style -->
    <Style x:Key="NavigationButton" TargetType="Button">
        <Setter Property="Height" Value="56"/>
        <Setter Property="Margin" Value="8,4"/>
        <Setter Property="Padding" Value="20,0"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="HorizontalContentAlignment" Value="Right"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="12"
                            BorderThickness="0">
                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryGradientBrush}"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Primary Button -->
    <Style x:Key="ActionButton" TargetType="Button">
        <Setter Property="Height" Value="48"/>
        <Setter Property="MinWidth" Value="120"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="24,0"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Background" Value="{StaticResource PrimaryGradientBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="24"
                            BorderThickness="0">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#667eea" BlurRadius="15" ShadowDepth="0" Opacity="0.4"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Secondary Button -->
    <Style x:Key="SecondaryButton" TargetType="Button">
        <Setter Property="Height" Value="48"/>
        <Setter Property="MinWidth" Value="120"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="24,0"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="24">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryBrush}"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Icon Button -->
    <Style x:Key="IconButton" TargetType="Button">
        <Setter Property="Width" Value="48"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="24"
                            BorderThickness="0">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource LightGrayBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource MediumGrayBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Search TextBox Style -->
    <Style x:Key="SearchTextBox" TargetType="TextBox">
        <Setter Property="Height" Value="48"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="24">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Search Icon -->
                            <materialDesign:PackIcon Grid.Column="0"
                                                   Kind="Magnify"
                                                   Width="20" Height="20"
                                                   Margin="16,0,8,0"
                                                   VerticalAlignment="Center"
                                                   Foreground="{StaticResource SecondaryTextBrush}"/>

                            <!-- Text Input -->
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Grid.Column="1"
                                        Focusable="false"
                                        HorizontalScrollBarVisibility="Hidden"
                                        VerticalScrollBarVisibility="Hidden"
                                        VerticalAlignment="Center"/>

                            <!-- Placeholder Text -->
                            <TextBlock x:Name="PlaceholderText"
                                     Grid.Column="1"
                                     Text="البحث..."
                                     Foreground="{StaticResource DisabledTextBrush}"
                                     VerticalAlignment="Center"
                                     IsHitTestVisible="False"
                                     Margin="8,0"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="border" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#667eea" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="Text" Value="">
                            <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                        </Trigger>
                        <Trigger Property="Text" Value="{x:Null}">
                            <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="Text" Value=""/>
                                <Condition Property="IsFocused" Value="False"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Form TextBox Style -->
    <Style x:Key="FormTextBox" TargetType="TextBox">
        <Setter Property="Height" Value="48"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="12">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Focusable="false"
                                    HorizontalScrollBarVisibility="Hidden"
                                    VerticalScrollBarVisibility="Hidden"
                                    Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="border" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#667eea" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource LightGrayBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource DisabledTextBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Form ComboBox Style -->
    <Style x:Key="FormComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignComboBox}">
        <Setter Property="Height" Value="40"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
    </Style>

    <!-- Form DatePicker Style -->
    <Style x:Key="FormDatePicker" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignDatePicker}">
        <Setter Property="Height" Value="40"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
    </Style>

    <!-- Form CheckBox Style -->
    <Style x:Key="FormCheckBox" TargetType="CheckBox" BasedOn="{StaticResource MaterialDesignCheckBox}">
        <Setter Property="Margin" Value="5"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- Label Style -->
    <Style x:Key="FormLabel" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Margin" Value="5,5,5,0"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
    </Style>

    <!-- Required Field Label Style -->
    <Style x:Key="RequiredLabel" TargetType="TextBlock" BasedOn="{StaticResource FormLabel}">
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
    </Style>

    <!-- Error Text Style -->
    <Style x:Key="ErrorText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="Red"/>
        <Setter Property="Margin" Value="5,0"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- Success Text Style -->
    <Style x:Key="SuccessText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="Green"/>
        <Setter Property="Margin" Value="5,0"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- Page Title Style -->
    <Style x:Key="PageTitle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="Margin" Value="0,0,0,20"/>
    </Style>

    <!-- Section Title Style -->
    <Style x:Key="SectionTitle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="Margin" Value="0,15,0,10"/>
    </Style>

    <!-- Toolbar Style -->
    <Style x:Key="ModernToolbar" TargetType="ToolBar" BasedOn="{StaticResource MaterialDesignToolBar}">
        <Setter Property="Background" Value="White"/>
        <Setter Property="Height" Value="50"/>
        <Setter Property="Padding" Value="10,5"/>
    </Style>

    <!-- Status Bar Style -->
    <Style x:Key="ModernStatusBar" TargetType="StatusBar">
        <Setter Property="Background" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="Height" Value="25"/>
        <Setter Property="FontSize" Value="11"/>
    </Style>

    <!-- Loading Indicator Style -->
    <Style x:Key="LoadingIndicator" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignCircularProgressBar}">
        <Setter Property="Width" Value="50"/>
        <Setter Property="Height" Value="50"/>
        <Setter Property="IsIndeterminate" Value="True"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Dialog Style -->
    <Style x:Key="ModernDialog" TargetType="Border">
        <Setter Property="Background" Value="White"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Effect" Value="{StaticResource MaterialDesignShadowDepth4}"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="MinWidth" Value="400"/>
        <Setter Property="MaxWidth" Value="800"/>
    </Style>

    <!-- Tab Style -->
    <Style x:Key="ModernTabControl" TargetType="TabControl" BasedOn="{StaticResource MaterialDesignTabControl}">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="0,10"/>
    </Style>

    <!-- Expander Style -->
    <Style x:Key="ModernExpander" TargetType="Expander" BasedOn="{StaticResource MaterialDesignExpander}">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="0,5"/>
        <Setter Property="Padding" Value="10"/>
    </Style>

    <!-- List View Style -->
    <Style x:Key="ModernListView" TargetType="ListView" BasedOn="{StaticResource MaterialDesignListView}">
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,10"/>
        <Setter Property="SelectionMode" Value="Single"/>
    </Style>

    <!-- Tree View Style -->
    <Style x:Key="ModernTreeView" TargetType="TreeView" BasedOn="{StaticResource MaterialDesignTreeView}">
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,10"/>
    </Style>

    <!-- Modern Window Style -->
    <Style x:Key="ModernWindow" TargetType="Window">
        <Setter Property="Background" Value="{StaticResource BackgroundBrush}"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- Modern ComboBox Style -->
    <Style x:Key="ModernComboBox" TargetType="ComboBox">
        <Setter Property="Height" Value="48"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="12">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <ContentPresenter Grid.Column="0"
                                            Content="{TemplateBinding SelectionBoxItem}"
                                            ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                            ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                            Margin="{TemplateBinding Padding}"
                                            VerticalAlignment="Center"
                                            HorizontalAlignment="Left"/>

                            <ToggleButton Grid.Column="1"
                                        x:Name="DropDownToggle"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        IsChecked="{Binding IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                        Width="32">
                                <materialDesign:PackIcon Kind="ChevronDown"
                                                       Width="16" Height="16"
                                                       Foreground="{StaticResource SecondaryTextBrush}"/>
                            </ToggleButton>

                            <Popup x:Name="PART_Popup"
                                   IsOpen="{TemplateBinding IsDropDownOpen}"
                                   Placement="Bottom"
                                   PlacementTarget="{Binding ElementName=border}">
                                <Border Background="{StaticResource SurfaceBrush}"
                                        BorderBrush="{StaticResource BorderBrush}"
                                        BorderThickness="1"
                                        CornerRadius="12"
                                        MinWidth="{TemplateBinding ActualWidth}"
                                        MaxHeight="200">
                                    <Border.Effect>
                                        <DropShadowEffect Color="#667eea" BlurRadius="15" ShadowDepth="0" Opacity="0.2"/>
                                    </Border.Effect>
                                    <ScrollViewer>
                                        <ItemsPresenter/>
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="border" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#667eea" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern CheckBox Style -->
    <Style x:Key="ModernCheckBox" TargetType="CheckBox">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <StackPanel Orientation="Horizontal">
                        <Border x:Name="checkBorder"
                                Width="20" Height="20"
                                Background="{StaticResource SurfaceBrush}"
                                BorderBrush="{StaticResource BorderBrush}"
                                BorderThickness="2"
                                CornerRadius="4"
                                Margin="0,0,8,0">
                            <materialDesign:PackIcon x:Name="checkIcon"
                                                   Kind="Check"
                                                   Width="12" Height="12"
                                                   Foreground="White"
                                                   Visibility="Collapsed"/>
                        </Border>
                        <ContentPresenter VerticalAlignment="Center"/>
                    </StackPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="checkBorder" Property="Background" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="checkBorder" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="checkIcon" Property="Visibility" Value="Visible"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="checkBorder" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern ProgressBar Style -->
    <Style x:Key="ModernProgressBar" TargetType="ProgressBar">
        <Setter Property="Height" Value="8"/>
        <Setter Property="Background" Value="{StaticResource LightGrayBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryGradientBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="4">
                        <Rectangle x:Name="PART_Track"
                                  Fill="{TemplateBinding Foreground}"
                                  RadiusX="4" RadiusY="4"
                                  HorizontalAlignment="Left"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Animation Styles -->
    <Style x:Key="FadeInAnimation" TargetType="FrameworkElement">
        <Style.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                       From="0" To="1"
                                       Duration="0:0:0.5"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="SlideInAnimation" TargetType="FrameworkElement">
        <Style.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                                       From="50" To="0"
                                       Duration="0:0:0.3"/>
                        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                       From="0" To="1"
                                       Duration="0:0:0.3"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>