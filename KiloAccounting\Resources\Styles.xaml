<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Modern Card Style -->
    <Style x:Key="ModernCard" TargetType="Border">
        <Setter Property="Background" Value="White"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Effect" Value="{StaticResource MaterialDesignShadowDepth2}"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Padding" Value="15"/>
    </Style>

    <!-- Data Grid Style -->
    <Style x:Key="ModernDataGrid" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="AlternatingRowBackground" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="RowBackground" Value="White"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,10"/>
    </Style>

    <!-- Form Section Style -->
    <Style x:Key="FormSection" TargetType="GroupBox" BasedOn="{StaticResource MaterialDesignGroupBox}">
        <Setter Property="Margin" Value="0,10"/>
        <Setter Property="Padding" Value="15"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- Form Grid Style -->
    <Style x:Key="FormGrid" TargetType="Grid">
        <Setter Property="Margin" Value="10"/>
    </Style>

    <!-- Action Button Style -->
    <Style x:Key="ActionButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Height" Value="35"/>
        <Setter Property="MinWidth" Value="100"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="FontSize" Value="13"/>
    </Style>

    <!-- Secondary Button Style -->
    <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="Height" Value="35"/>
        <Setter Property="MinWidth" Value="100"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="FontSize" Value="13"/>
    </Style>

    <!-- Icon Button Style -->
    <Style x:Key="IconButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
        <Setter Property="Width" Value="35"/>
        <Setter Property="Height" Value="35"/>
        <Setter Property="Margin" Value="2"/>
    </Style>

    <!-- Search TextBox Style -->
    <Style x:Key="SearchTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
        <Setter Property="Height" Value="40"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="materialDesign:HintAssist.Hint" Value="البحث..."/>
        <Setter Property="materialDesign:TextFieldAssist.HasLeadingIcon" Value="True"/>
        <Setter Property="materialDesign:TextFieldAssist.LeadingIcon" Value="Magnify"/>
    </Style>

    <!-- Form TextBox Style -->
    <Style x:Key="FormTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
        <Setter Property="Height" Value="40"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
    </Style>

    <!-- Form ComboBox Style -->
    <Style x:Key="FormComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignComboBox}">
        <Setter Property="Height" Value="40"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
    </Style>

    <!-- Form DatePicker Style -->
    <Style x:Key="FormDatePicker" TargetType="DatePicker" BasedOn="{StaticResource MaterialDesignDatePicker}">
        <Setter Property="Height" Value="40"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
    </Style>

    <!-- Form CheckBox Style -->
    <Style x:Key="FormCheckBox" TargetType="CheckBox" BasedOn="{StaticResource MaterialDesignCheckBox}">
        <Setter Property="Margin" Value="5"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- Label Style -->
    <Style x:Key="FormLabel" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Margin" Value="5,5,5,0"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
    </Style>

    <!-- Required Field Label Style -->
    <Style x:Key="RequiredLabel" TargetType="TextBlock" BasedOn="{StaticResource FormLabel}">
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
    </Style>

    <!-- Error Text Style -->
    <Style x:Key="ErrorText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="Red"/>
        <Setter Property="Margin" Value="5,0"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- Success Text Style -->
    <Style x:Key="SuccessText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="Green"/>
        <Setter Property="Margin" Value="5,0"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- Page Title Style -->
    <Style x:Key="PageTitle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
        <Setter Property="Margin" Value="0,0,0,20"/>
    </Style>

    <!-- Section Title Style -->
    <Style x:Key="SectionTitle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="Margin" Value="0,15,0,10"/>
    </Style>

    <!-- Toolbar Style -->
    <Style x:Key="ModernToolbar" TargetType="ToolBar" BasedOn="{StaticResource MaterialDesignToolBar}">
        <Setter Property="Background" Value="White"/>
        <Setter Property="Height" Value="50"/>
        <Setter Property="Padding" Value="10,5"/>
    </Style>

    <!-- Status Bar Style -->
    <Style x:Key="ModernStatusBar" TargetType="StatusBar">
        <Setter Property="Background" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="Height" Value="25"/>
        <Setter Property="FontSize" Value="11"/>
    </Style>

    <!-- Loading Indicator Style -->
    <Style x:Key="LoadingIndicator" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignCircularProgressBar}">
        <Setter Property="Width" Value="50"/>
        <Setter Property="Height" Value="50"/>
        <Setter Property="IsIndeterminate" Value="True"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Dialog Style -->
    <Style x:Key="ModernDialog" TargetType="Border">
        <Setter Property="Background" Value="White"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Effect" Value="{StaticResource MaterialDesignShadowDepth4}"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="MinWidth" Value="400"/>
        <Setter Property="MaxWidth" Value="800"/>
    </Style>

    <!-- Tab Style -->
    <Style x:Key="ModernTabControl" TargetType="TabControl" BasedOn="{StaticResource MaterialDesignTabControl}">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="0,10"/>
    </Style>

    <!-- Expander Style -->
    <Style x:Key="ModernExpander" TargetType="Expander" BasedOn="{StaticResource MaterialDesignExpander}">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="0,5"/>
        <Setter Property="Padding" Value="10"/>
    </Style>

    <!-- List View Style -->
    <Style x:Key="ModernListView" TargetType="ListView" BasedOn="{StaticResource MaterialDesignListView}">
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,10"/>
        <Setter Property="SelectionMode" Value="Single"/>
    </Style>

    <!-- Tree View Style -->
    <Style x:Key="ModernTreeView" TargetType="TreeView" BasedOn="{StaticResource MaterialDesignTreeView}">
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,10"/>
    </Style>

</ResourceDictionary>