<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>Assets\logo.ico</ApplicationIcon>
    <AssemblyTitle>Kilo Accounting - Professional Financial Management System</AssemblyTitle>
    <AssemblyDescription>Professional accounting application for companies</AssemblyDescription>
    <AssemblyCompany>Kilo Software Solutions</AssemblyCompany>
    <AssemblyProduct>Kilo Accounting</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Copyright>Copyright © 2025 Kilo Software Solutions</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MaterialDesignThemes" Version="4.8.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.14" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.118" />
    <PackageReference Include="EPPlus" Version="7.0.4" />
    <PackageReference Include="iTextSharp" Version="********" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Assets\" />
    <Folder Include="Models\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Views\" />
    <Folder Include="Services\" />
    <Folder Include="Data\" />
    <Folder Include="Helpers\" />
    <Folder Include="Resources\" />
    <Folder Include="Reports\" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Assets\**" />
    <Resource Include="Resources\**" />
  </ItemGroup>

</Project>