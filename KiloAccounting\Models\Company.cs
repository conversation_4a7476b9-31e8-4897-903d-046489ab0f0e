using System;
using System.ComponentModel.DataAnnotations;

namespace KiloAccounting.Models
{
    public class Company
    {
        [Key]
        public int CompanyId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        [StringLength(100)]
        public string NameEnglish { get; set; }

        [StringLength(200)]
        public string Address { get; set; }

        [StringLength(200)]
        public string AddressEnglish { get; set; }

        [StringLength(20)]
        public string Phone { get; set; }

        [StringLength(20)]
        public string Mobile { get; set; }

        [StringLength(20)]
        public string Fax { get; set; }

        [StringLength(100)]
        public string Email { get; set; }

        [StringLength(100)]
        public string Website { get; set; }

        [StringLength(50)]
        public string TaxNumber { get; set; }

        [StringLength(50)]
        public string CommercialRegister { get; set; }

        public byte[] Logo { get; set; }

        [Required]
        public DateTime FiscalYearStart { get; set; }

        [Required]
        public DateTime FiscalYearEnd { get; set; }

        [Required]
        [StringLength(3)]
        public string Currency { get; set; } = "SAR";

        [StringLength(50)]
        public string CurrencySymbol { get; set; } = "ر.س";

        public int DecimalPlaces { get; set; } = 2;

        public bool UseVAT { get; set; } = false;

        public decimal VATRate { get; set; } = 0.15m; // 15% VAT

        [StringLength(10)]
        public string Language { get; set; } = "ar-SA";

        public bool IsRTL { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        // Financial Settings
        public decimal OpeningBalance { get; set; } = 0;

        [StringLength(20)]
        public string InvoicePrefix { get; set; } = "INV";

        [StringLength(20)]
        public string PurchasePrefix { get; set; } = "PUR";

        [StringLength(20)]
        public string QuotationPrefix { get; set; } = "QUO";

        public int NextInvoiceNumber { get; set; } = 1;

        public int NextPurchaseNumber { get; set; } = 1;

        public int NextQuotationNumber { get; set; } = 1;

        // Backup Settings
        public bool AutoBackup { get; set; } = true;

        public int BackupIntervalDays { get; set; } = 7;

        [StringLength(500)]
        public string BackupPath { get; set; }
    }
}