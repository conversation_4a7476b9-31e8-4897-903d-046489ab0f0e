using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace KiloAccounting.Models
{
    public class PurchaseInvoice
    {
        [Key]
        public int PurchaseInvoiceId { get; set; }

        [Required]
        [StringLength(50)]
        public string InvoiceNumber { get; set; }

        [StringLength(50)]
        public string SupplierInvoiceNumber { get; set; }

        public DateTime InvoiceDate { get; set; }

        public DateTime DueDate { get; set; }

        public int SupplierId { get; set; }

        [ForeignKey("SupplierId")]
        public virtual Supplier Supplier { get; set; }

        public PurchaseInvoiceType InvoiceType { get; set; }

        public PurchaseInvoiceStatus Status { get; set; } = PurchaseInvoiceStatus.Draft;

        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal VATAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingAmount { get; set; } = 0;

        [StringLength(500)]
        public string Notes { get; set; }

        [StringLength(500)]
        public string Terms { get; set; }

        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;

        [StringLength(50)]
        public string ReferenceNumber { get; set; }

        public bool IsPosted { get; set; } = false;

        public DateTime? PostedDate { get; set; }

        public int? PostedBy { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? ModifiedDate { get; set; }

        public int CreatedBy { get; set; }

        // Navigation properties
        public virtual ICollection<PurchaseInvoiceItem> PurchaseInvoiceItems { get; set; } = new List<PurchaseInvoiceItem>();
        public virtual ICollection<PurchaseInvoicePayment> Payments { get; set; } = new List<PurchaseInvoicePayment>();
        public virtual ICollection<SupplierTransaction> SupplierTransactions { get; set; } = new List<SupplierTransaction>();
    }

    public class PurchaseInvoiceItem
    {
        [Key]
        public int PurchaseInvoiceItemId { get; set; }

        public int PurchaseInvoiceId { get; set; }

        [ForeignKey("PurchaseInvoiceId")]
        public virtual PurchaseInvoice PurchaseInvoice { get; set; }

        public int ProductId { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [StringLength(200)]
        public string Description { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Quantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal NetAmount { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal VATRate { get; set; } = 0.15m;

        [Column(TypeName = "decimal(18,2)")]
        public decimal VATAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        public int LineNumber { get; set; }
    }

    public class PurchaseInvoicePayment
    {
        [Key]
        public int PaymentId { get; set; }

        public int PurchaseInvoiceId { get; set; }

        [ForeignKey("PurchaseInvoiceId")]
        public virtual PurchaseInvoice PurchaseInvoice { get; set; }

        public DateTime PaymentDate { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        public PaymentMethod PaymentMethod { get; set; }

        [StringLength(50)]
        public string ReferenceNumber { get; set; }

        [StringLength(200)]
        public string Notes { get; set; }

        public int CreatedBy { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }

    public enum PurchaseInvoiceType
    {
        Purchase = 1,           // مشتريات
        PurchaseReturn = 2,     // مرتجع مشتريات
        PurchaseOrder = 3       // أمر شراء
    }

    public enum PurchaseInvoiceStatus
    {
        Draft = 1,              // مسودة
        Pending = 2,            // معلقة
        Approved = 3,           // معتمدة
        Received = 4,           // مستلمة
        Paid = 5,               // مدفوعة
        PartiallyPaid = 6,      // مدفوعة جزئياً
        Overdue = 7,            // متأخرة
        Cancelled = 8,          // ملغاة
        Returned = 9            // مرتجعة
    }
}