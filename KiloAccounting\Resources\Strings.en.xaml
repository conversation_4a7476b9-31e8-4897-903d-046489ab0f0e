<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!-- Application -->
    <system:String x:Key="AppName">Kilo Financial Accounting System</system:String>
    <system:String x:Key="AppVersion">Version 1.0.0</system:String>
    <system:String x:Key="CompanyName">Kilo Software Solutions</system:String>

    <!-- Login -->
    <system:String x:Key="Login">Login</system:String>
    <system:String x:Key="Username">Username</system:String>
    <system:String x:Key="Password">Password</system:String>
    <system:String x:Key="RememberMe">Remember Me</system:String>
    <system:String x:Key="ForgotPassword">Forgot Password?</system:String>
    <system:String x:Key="ResetPassword">Reset</system:String>
    <system:String x:Key="Logout">Logout</system:String>

    <!-- Navigation -->
    <system:String x:Key="Dashboard">Dashboard</system:String>
    <system:String x:Key="Sales">Sales</system:String>
    <system:String x:Key="Purchases">Purchases</system:String>
    <system:String x:Key="Inventory">Inventory</system:String>
    <system:String x:Key="Customers">Customers</system:String>
    <system:String x:Key="Suppliers">Suppliers</system:String>
    <system:String x:Key="Accounting">Accounting</system:String>
    <system:String x:Key="Reports">Reports</system:String>
    <system:String x:Key="Settings">Settings</system:String>

    <!-- Sales -->
    <system:String x:Key="SalesInvoices">Sales Invoices</system:String>
    <system:String x:Key="SalesReturns">Sales Returns</system:String>
    <system:String x:Key="NewSalesInvoice">New Sales Invoice</system:String>
    <system:String x:Key="EditSalesInvoice">Edit Sales Invoice</system:String>

    <!-- Purchases -->
    <system:String x:Key="PurchaseInvoices">Purchase Invoices</system:String>
    <system:String x:Key="PurchaseReturns">Purchase Returns</system:String>
    <system:String x:Key="NewPurchaseInvoice">New Purchase Invoice</system:String>
    <system:String x:Key="EditPurchaseInvoice">Edit Purchase Invoice</system:String>

    <!-- Inventory -->
    <system:String x:Key="Products">Products</system:String>
    <system:String x:Key="Categories">Categories</system:String>
    <system:String x:Key="Units">Units</system:String>
    <system:String x:Key="StockMovements">Stock Movements</system:String>
    <system:String x:Key="InventoryAdjustment">Inventory Adjustment</system:String>

    <!-- Customers & Suppliers -->
    <system:String x:Key="NewCustomer">New Customer</system:String>
    <system:String x:Key="EditCustomer">Edit Customer</system:String>
    <system:String x:Key="CustomerStatement">Customer Statement</system:String>
    <system:String x:Key="NewSupplier">New Supplier</system:String>
    <system:String x:Key="EditSupplier">Edit Supplier</system:String>
    <system:String x:Key="SupplierStatement">Supplier Statement</system:String>

    <!-- Accounting -->
    <system:String x:Key="ChartOfAccounts">Chart of Accounts</system:String>
    <system:String x:Key="JournalEntries">Journal Entries</system:String>
    <system:String x:Key="TrialBalance">Trial Balance</system:String>
    <system:String x:Key="GeneralLedger">General Ledger</system:String>

    <!-- Reports -->
    <system:String x:Key="FinancialReports">Financial Reports</system:String>
    <system:String x:Key="IncomeStatement">Income Statement</system:String>
    <system:String x:Key="BalanceSheet">Balance Sheet</system:String>
    <system:String x:Key="CashFlow">Cash Flow</system:String>
    <system:String x:Key="InventoryReports">Inventory Reports</system:String>
    <system:String x:Key="SalesReports">Sales Reports</system:String>
    <system:String x:Key="PurchaseReports">Purchase Reports</system:String>

    <!-- Common Fields -->
    <system:String x:Key="Code">Code</system:String>
    <system:String x:Key="Name">Name</system:String>
    <system:String x:Key="Description">Description</system:String>
    <system:String x:Key="Date">Date</system:String>
    <system:String x:Key="Amount">Amount</system:String>
    <system:String x:Key="Quantity">Quantity</system:String>
    <system:String x:Key="Price">Price</system:String>
    <system:String x:Key="Total">Total</system:String>
    <system:String x:Key="Subtotal">Subtotal</system:String>
    <system:String x:Key="Discount">Discount</system:String>
    <system:String x:Key="VAT">VAT</system:String>
    <system:String x:Key="Status">Status</system:String>
    <system:String x:Key="Notes">Notes</system:String>

    <!-- Actions -->
    <system:String x:Key="Add">Add</system:String>
    <system:String x:Key="Edit">Edit</system:String>
    <system:String x:Key="Delete">Delete</system:String>
    <system:String x:Key="Save">Save</system:String>
    <system:String x:Key="Cancel">Cancel</system:String>
    <system:String x:Key="Search">Search</system:String>
    <system:String x:Key="Print">Print</system:String>
    <system:String x:Key="Export">Export</system:String>
    <system:String x:Key="Import">Import</system:String>
    <system:String x:Key="Refresh">Refresh</system:String>
    <system:String x:Key="Close">Close</system:String>
    <system:String x:Key="OK">OK</system:String>
    <system:String x:Key="Yes">Yes</system:String>
    <system:String x:Key="No">No</system:String>

    <!-- Status -->
    <system:String x:Key="Active">Active</system:String>
    <system:String x:Key="Inactive">Inactive</system:String>
    <system:String x:Key="Draft">Draft</system:String>
    <system:String x:Key="Pending">Pending</system:String>
    <system:String x:Key="Approved">Approved</system:String>
    <system:String x:Key="Posted">Posted</system:String>
    <system:String x:Key="Paid">Paid</system:String>
    <system:String x:Key="Unpaid">Unpaid</system:String>
    <system:String x:Key="PartiallyPaid">Partially Paid</system:String>
    <system:String x:Key="Overdue">Overdue</system:String>
    <system:String x:Key="Cancelled">Cancelled</system:String>

    <!-- User Roles -->
    <system:String x:Key="Manager">Manager</system:String>
    <system:String x:Key="Accountant">Accountant</system:String>
    <system:String x:Key="SalesEmployee">Sales Employee</system:String>
    <system:String x:Key="PurchaseEmployee">Purchase Employee</system:String>
    <system:String x:Key="InventoryEmployee">Inventory Employee</system:String>
    <system:String x:Key="Viewer">Viewer</system:String>

    <!-- Messages -->
    <system:String x:Key="SaveSuccess">Saved successfully</system:String>
    <system:String x:Key="DeleteSuccess">Deleted successfully</system:String>
    <system:String x:Key="UpdateSuccess">Updated successfully</system:String>
    <system:String x:Key="Error">Error</system:String>
    <system:String x:Key="Warning">Warning</system:String>
    <system:String x:Key="Information">Information</system:String>
    <system:String x:Key="Confirmation">Confirmation</system:String>
    <system:String x:Key="Loading">Loading...</system:String>
    <system:String x:Key="NoDataFound">No data found</system:String>
    <system:String x:Key="InvalidData">Invalid data</system:String>
    <system:String x:Key="RequiredField">This field is required</system:String>

    <!-- Dashboard -->
    <system:String x:Key="TodaySales">Today's Sales</system:String>
    <system:String x:Key="TodayPurchases">Today's Purchases</system:String>
    <system:String x:Key="TotalCustomers">Total Customers</system:String>
    <system:String x:Key="LowStockItems">Low Stock Items</system:String>
    <system:String x:Key="RecentActivities">Recent Activities</system:String>
    <system:String x:Key="QuickActions">Quick Actions</system:String>

    <!-- Company Settings -->
    <system:String x:Key="CompanySettings">Company Settings</system:String>
    <system:String x:Key="CompanyName">Company Name</system:String>
    <system:String x:Key="CompanyAddress">Company Address</system:String>
    <system:String x:Key="CompanyPhone">Company Phone</system:String>
    <system:String x:Key="CompanyEmail">Company Email</system:String>
    <system:String x:Key="TaxNumber">Tax Number</system:String>
    <system:String x:Key="CommercialRegister">Commercial Register</system:String>
    <system:String x:Key="FiscalYear">Fiscal Year</system:String>
    <system:String x:Key="Currency">Currency</system:String>

    <!-- Backup -->
    <system:String x:Key="Backup">Backup</system:String>
    <system:String x:Key="CreateBackup">Create Backup</system:String>
    <system:String x:Key="RestoreBackup">Restore Backup</system:String>
    <system:String x:Key="BackupSuccess">Backup created successfully</system:String>
    <system:String x:Key="RestoreSuccess">Backup restored successfully</system:String>

</ResourceDictionary>